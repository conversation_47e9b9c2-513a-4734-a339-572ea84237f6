#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的GZ method K值计算和判定标准
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关函数
from Pile_analyze_GZ_gui_final_M import determine_final_category, calculate_K_i

def test_k_calculation():
    """测试K值计算公式"""

    print("=" * 60)
    print("测试K值计算公式修复")
    print("=" * 60)

    # 测试您提供的实际数据
    print("\n测试实际数据的K值计算：")

    # 深度 3.60m的数据
    print("\n深度 3.60m:")
    I_ji_values_360 = [4, 3, 3]  # 剖面1-2: 4, 剖面1-3: 3, 剖面2-3: 3
    K_i_360 = calculate_K_i(I_ji_values_360)
    print(f"I(j,i)值: {I_ji_values_360}")
    print(f"计算过程: K_i = int((∑I(j,i)² / ∑I(j,i)) + 0.5)")
    print(f"         = int(({4**2} + {3**2} + {3**2}) / ({4} + {3} + {3}) + 0.5)")
    print(f"         = int((16 + 9 + 9) / 10 + 0.5)")
    print(f"         = int(34 / 10 + 0.5)")
    print(f"         = int(3.4 + 0.5)")
    print(f"         = int(3.9)")
    print(f"         = 3")
    print(f"实际计算结果: K_i = {K_i_360}")

    # 深度 3.70m的数据
    print("\n深度 3.70m:")
    I_ji_values_370 = [1, 2, 1]  # 剖面1-2: 1, 剖面1-3: 2, 剖面2-3: 1
    K_i_370 = calculate_K_i(I_ji_values_370)
    print(f"I(j,i)值: {I_ji_values_370}")
    print(f"计算过程: K_i = int((∑I(j,i)² / ∑I(j,i)) + 0.5)")
    print(f"         = int(({1**2} + {2**2} + {1**2}) / ({1} + {2} + {1}) + 0.5)")
    print(f"         = int((1 + 4 + 1) / 4 + 0.5)")
    print(f"         = int(6 / 4 + 0.5)")
    print(f"         = int(1.5 + 0.5)")
    print(f"         = int(2.0)")
    print(f"         = 2")
    print(f"实际计算结果: K_i = {K_i_370}")

def test_k_determination():
    """测试新的K值判定逻辑"""

    print("\n" + "=" * 60)
    print("测试新的GZ method K值判定标准")
    print("=" * 60)
    
    # 测试用例1: I类桩 - 所有K值均为1
    print("\n测试用例1: I类桩")
    k_values_1 = {0.1: 1, 0.2: 1, 0.3: 1, 0.4: 1, 0.5: 1}
    category_1, details_1 = determine_final_category(k_values_1)
    print(f"K值分布: {k_values_1}")
    print(f"判定结果: {category_1}")
    print(f"判定依据: {details_1}")
    
    # 测试用例2: II类桩 - 仅存在一个K=2
    print("\n测试用例2: II类桩 (仅一个K=2)")
    k_values_2 = {0.1: 1, 0.2: 1, 0.3: 2, 0.4: 1, 0.5: 1}
    category_2, details_2 = determine_final_category(k_values_2)
    print(f"K值分布: {k_values_2}")
    print(f"判定结果: {category_2}")
    print(f"判定依据: {details_2}")
    
    # 测试用例3: II类桩 - 多个K=2但不连续
    print("\n测试用例3: II类桩 (多个K=2但不连续)")
    k_values_3 = {0.1: 1, 0.2: 2, 0.3: 1, 0.4: 1, 0.5: 1, 0.6: 2, 0.7: 1}
    category_3, details_3 = determine_final_category(k_values_3)
    print(f"K值分布: {k_values_3}")
    print(f"判定结果: {category_3}")
    print(f"判定依据: {details_3}")
    
    # 测试用例4: III类桩 - 仅存在一个K=3
    print("\n测试用例4: III类桩 (仅一个K=3)")
    k_values_4 = {0.1: 1, 0.2: 1, 0.3: 3, 0.4: 1, 0.5: 1}
    category_4, details_4 = determine_final_category(k_values_4)
    print(f"K值分布: {k_values_4}")
    print(f"判定结果: {category_4}")
    print(f"判定依据: {details_4}")
    
    # 测试用例5: III类桩 - 多个K=3且距离≥50cm
    print("\n测试用例5: III类桩 (多个K=3且距离≥50cm)")
    k_values_5 = {0.1: 1, 0.2: 3, 0.3: 1, 0.4: 1, 0.5: 1, 0.6: 1, 0.7: 1, 0.8: 3, 0.9: 1}
    category_5, details_5 = determine_final_category(k_values_5)
    print(f"K值分布: {k_values_5}")
    print(f"判定结果: {category_5}")
    print(f"判定依据: {details_5}")
    
    # 测试用例6: III类桩 - 多个K=2且连续50cm
    print("\n测试用例6: III类桩 (多个K=2且连续50cm)")
    k_values_6 = {0.1: 1, 0.2: 2, 0.3: 2, 0.4: 2, 0.5: 2, 0.6: 2, 0.7: 2, 0.8: 1}
    category_6, details_6 = determine_final_category(k_values_6)
    print(f"K值分布: {k_values_6}")
    print(f"判定结果: {category_6}")
    print(f"判定依据: {details_6}")
    
    # 测试用例7: IV类桩 - 仅存在一个K=4
    print("\n测试用例7: IV类桩 (仅一个K=4)")
    k_values_7 = {0.1: 1, 0.2: 1, 0.3: 4, 0.4: 1, 0.5: 1}
    category_7, details_7 = determine_final_category(k_values_7)
    print(f"K值分布: {k_values_7}")
    print(f"判定结果: {category_7}")
    print(f"判定依据: {details_7}")
    
    # 测试用例8: IV类桩 - 多个K=3且连续50cm
    print("\n测试用例8: IV类桩 (多个K=3且连续50cm)")
    k_values_8 = {0.1: 1, 0.2: 3, 0.3: 3, 0.4: 3, 0.5: 3, 0.6: 3, 0.7: 3, 0.8: 1}
    category_8, details_8 = determine_final_category(k_values_8)
    print(f"K值分布: {k_values_8}")
    print(f"判定结果: {category_8}")
    print(f"判定依据: {details_8}")
    
    # 测试用例9: 边界情况 - 多个K=3但距离<50cm (应该是III类)
    print("\n测试用例9: 边界情况 (多个K=3但距离<50cm)")
    k_values_9 = {0.1: 1, 0.2: 3, 0.3: 1, 0.4: 3, 0.5: 1}  # 距离只有20cm
    category_9, details_9 = determine_final_category(k_values_9)
    print(f"K值分布: {k_values_9}")
    print(f"判定结果: {category_9}")
    print(f"判定依据: {details_9}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_k_calculation()
    test_k_determination()
