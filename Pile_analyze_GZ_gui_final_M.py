#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Professional GUI for Pile Integrity Analyzer (GZ Method)
桩基完整性分析系统专业GUI (GZ方法)

This GUI provides a commercial-grade interface for:
- Traditional pile integrity analysis using GZ method
- AI-enhanced analysis with machine learning
- Comparative analysis between methods
- Advanced visualization and reporting
- Model training and configuration

Author: Pile Integrity Analysis System (GZ Method)
Version: 1.6 (Fixed NumPy 2.0 compatibility and refined AI Roman numeral mapping)
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import threading
import queue
import os
import sys
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from matplotlib.ticker import MaxNLocator
import matplotlib.colors as mcolors
import matplotlib.font_manager as fm
import seaborn as sns
from datetime import datetime
import time
import traceback # Added for better error logging

# Import AI and ML libraries for built-in AI functionality
import pickle
import joblib
from sklearn.ensemble import IsolationForest, RandomForestClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from collections import defaultdict
import warnings
try:
    import plotly.graph_objects as go
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    print("Plotly not available. 3D visualization will be disabled.")
warnings.filterwarnings('ignore')

# --- Font Configuration for Chinese Characters ---
def configure_chinese_fonts():
    """
    Configure matplotlib to properly display Chinese characters.
    Tries multiple Chinese fonts and provides fallback options.
    """
    chinese_fonts = [
        'Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong',
        'PingFang SC', 'Hiragino Sans GB', 'WenQuanYi Micro Hei',
        'Noto Sans CJK SC', 'Source Han Sans SC'
    ]
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            break
    if selected_font:
        try:
            plt.rcParams['font.sans-serif'] = [selected_font] + plt.rcParams['font.sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            print(f"Successfully configured Chinese font: {selected_font}")
            return True
        except Exception as e:
            print(f"Warning: Error configuring font {selected_font}: {e}")
    print("Warning: No available Chinese font found. Chinese characters in charts may display as squares.")
    return False

# --- GZ Method Core Calculation Logic ---
DEFAULT_GZ_CONFIG = {
    'Sp_conditions': {
        'ge_100': lambda sp: sp >= 100, '85_lt_100': lambda sp: 85 <= sp < 100,
        '75_lt_85': lambda sp: 75 <= sp < 85, '65_lt_75': lambda sp: 65 <= sp < 75,
        'lt_65': lambda sp: sp < 65, 'ge_85': lambda sp: sp >= 85,
        'ge_75': lambda sp: sp >= 75, 'ge_65': lambda sp: sp >= 65,
    },
    'Ad_conditions': {
        'le_0': lambda ad: ad <= 0, 'gt_0_le_4': lambda ad: 0 < ad <= 4,
        'gt_4_le_8': lambda ad: 4 < ad <= 8, 'gt_8_le_12': lambda ad: 8 < ad <= 12,
        'gt_12': lambda ad: ad > 12, 'le_4': lambda ad: ad <=4,
        'le_8': lambda ad: ad <=8, 'le_12': lambda ad: ad <=12,
    },
    'Energy_conditions': {
        'I_1': lambda e: 0.8 <= e <= 100.0,  # I(j,i)=1: 正常
        'I_2': lambda e: 0.5 <= e < 0.8,      # I(j,i)=2: 轻微畸变
        'I_3': lambda e: 0.25 <= e < 0.5,     # I(j,i)=3: 明显畸变
        'I_4': lambda e: 0.0 <= e < 0.25,     # I(j,i)=4: 严重畸变
        'normal': lambda e: 0.8 <= e <= 100.0, 'light': lambda e: 0.5 <= e < 0.8,
        'obvious': lambda e: 0.25 <= e < 0.5, 'severe': lambda e: 0.0 <= e < 0.25,
        'ge_08': lambda e: e >= 0.8, 'ge_05': lambda e: e >= 0.5, 'ge_025': lambda e: e >= 0.25
    },
    'PSD_conditions': {
        'I_1': lambda p: 0.0 <= p <= 1.0,     # I(j,i)=1: 正常
        'I_2': lambda p: 1.0 < p <= 2.0,      # I(j,i)=2: 轻微畸变
        'I_3': lambda p: 2.0 < p <= 3.0,      # I(j,i)=3: 明显畸变
        'I_4': lambda p: 3.0 < p <= 100.0,    # I(j,i)=4: 严重畸变
        'normal': lambda p: 0.0 <= p <= 1.0, 'light': lambda p: 1.0 < p <= 2.0,
        'obvious': lambda p: 2.0 < p <= 3.0, 'severe': lambda p: 3.0 < p <= 100.0,
        'le_1': lambda p: p <= 1.0, 'le_2': lambda p: p <= 2.0, 'le_3': lambda p: p <= 3.0
    },
    'Bi_ratio_conditions': {
        'gt_08': lambda br: br > 0.8, 'gt_05_le_08': lambda br: 0.5 < br <= 0.8,
        'gt_05': lambda br: br > 0.5, 'gt_025_le_05': lambda br: 0.25 < br <= 0.5,
        'gt_025': lambda br: br > 0.25, 'le_025': lambda br: br <= 0.25,
    }
}

# --- New Enhanced Analysis System ---
DEFAULT_ENHANCED_CONFIG = {
    'speed_thresholds': {
        '正常': (100.0, 1000.0),
        '轻微畸变': (85.0, 100.0),
        '明显畸变': (75.0, 85.0),
        '严重畸变': (0.0, 75.0)
    },
    'amp_thresholds': {
        '正常': (-100, 3),
        '轻微畸变': (3, 6),
        '明显畸变': (6, 12),
        '严重畸变': (12, 100)
    },
    'energy_thresholds': {
        '正常': (0.8, 100),
        '轻微畸变': (0.5, 0.8),
        '明显畸变': (0.25, 0.5),
        '严重畸变': (0, 0.25)
    },
    'psd_thresholds': {
        '正常': (0, 1),
        '轻微畸变': (1, 2),
        '明显畸变': (2, 3),
        '严重畸变': (3, 100)
    },
    'continuous_threshold': 0.5,  # 连续长度阈值(m)
    'enabled_indicators': {
        'speed': True,
        'amp': True,
        'energy': True,
        'psd': True
    }
}

def calculate_I_ji_enhanced(Sp, Ad, Energy=None, PSD=None, config=DEFAULT_GZ_CONFIG, enabled_indicators=None):
    """
    增强的I(j,i)计算函数，按照GZ Traditional Analysis公式
    I(j,i)=1：Speed%(100.0-1000.0)，Amp%(-100-3)，Energy%(0.8-100), PSD(0-1)
    I(j,i)=2：Speed%(85.0-100.0)，Amp%(3-6)，Energy%(0.5-0.8), PSD(1-2)
    I(j,i)=3：Speed%(75.0-85.0)，Amp%(6-12)，Energy%(0.25-0.5), PSD(2-3)
    I(j,i)=4：Speed%(65.0-75.0)，Amp%(12-100)，Energy%(0-0.25), PSD(3-100)
    """
    if enabled_indicators is None:
        enabled_indicators = {'speed': True, 'amplitude': True, 'energy': False, 'psd': False}

    sp_cond = config['Sp_conditions']
    ad_cond = config['Ad_conditions']
    energy_cond = config.get('Energy_conditions', {})
    psd_cond = config.get('PSD_conditions', {})

    # 计算各指标的I(j,i)值
    i_values = []

    # Speed% 分类
    if enabled_indicators.get('speed', True) and Sp is not None:
        if 100.0 <= Sp <= 1000.0:
            i_values.append(1)  # I(j,i)=1
        elif 85.0 <= Sp < 100.0:
            i_values.append(2)  # I(j,i)=2
        elif 75.0 <= Sp < 85.0:
            i_values.append(3)  # I(j,i)=3
        elif 65.0 <= Sp < 75.0:
            i_values.append(4)  # I(j,i)=4
        else:
            i_values.append(4)  # 超出范围，视为严重畸变

    # Amplitude 分类
    if enabled_indicators.get('amplitude', True) and Ad is not None:
        if -100 <= Ad <= 3:
            i_values.append(1)  # I(j,i)=1
        elif 3 < Ad <= 6:
            i_values.append(2)  # I(j,i)=2
        elif 6 < Ad <= 12:
            i_values.append(3)  # I(j,i)=3
        elif 12 < Ad <= 100:
            i_values.append(4)  # I(j,i)=4
        else:
            i_values.append(4)  # 超出范围，视为严重畸变

    # Energy% 分类
    if enabled_indicators.get('energy', False) and Energy is not None:
        if 0.8 <= Energy <= 100:
            i_values.append(1)  # I(j,i)=1
        elif 0.5 <= Energy < 0.8:
            i_values.append(2)  # I(j,i)=2
        elif 0.25 <= Energy < 0.5:
            i_values.append(3)  # I(j,i)=3
        elif 0 <= Energy < 0.25:
            i_values.append(4)  # I(j,i)=4
        else:
            i_values.append(4)  # 超出范围，视为严重畸变

    # PSD 分类
    if enabled_indicators.get('psd', False) and PSD is not None:
        if 0 <= PSD <= 1:
            i_values.append(1)  # I(j,i)=1
        elif 1 < PSD <= 2:
            i_values.append(2)  # I(j,i)=2
        elif 2 < PSD <= 3:
            i_values.append(3)  # I(j,i)=3
        elif 3 < PSD <= 100:
            i_values.append(4)  # I(j,i)=4
        else:
            i_values.append(4)  # 超出范围，视为严重畸变

    # 如果没有启用任何指标，返回1（正常）
    if not i_values:
        return 1

    # 取最严重的分类作为最终I(j,i)值
    return max(i_values)

def calculate_K_value_enhanced(Sp, Ad, Energy=None, PSD=None, Bi_ratio=1.0, config=DEFAULT_GZ_CONFIG, enabled_indicators=None):
    """
    增强的K值计算函数，支持Speed%, Amp%, Energy%, PSD四个指标
    """
    if enabled_indicators is None:
        enabled_indicators = {'speed': True, 'amplitude': True, 'energy': False, 'psd': False}

    sp_cond = config['Sp_conditions']
    ad_cond = config['Ad_conditions']
    energy_cond = config.get('Energy_conditions', {})
    psd_cond = config.get('PSD_conditions', {})

    # 计算各指标的分类等级 (1=正常, 2=轻微, 3=明显, 4=严重)
    classifications = []

    # Speed% 分类
    if enabled_indicators.get('speed', True):
        if sp_cond['ge_100'](Sp):
            classifications.append(1)  # 正常
        elif sp_cond['85_lt_100'](Sp):
            classifications.append(2)  # 轻微畸变
        elif sp_cond['75_lt_85'](Sp):
            classifications.append(3)  # 明显畸变
        elif sp_cond['65_lt_75'](Sp):
            classifications.append(4)  # 严重畸变
        else:
            classifications.append(4)  # 超出范围，视为严重畸变

    # Amplitude 分类
    if enabled_indicators.get('amplitude', True):
        if ad_cond['le_0'](Ad):
            classifications.append(1)  # 正常
        elif ad_cond['gt_0_le_4'](Ad):
            classifications.append(2)  # 轻微畸变
        elif ad_cond['gt_4_le_8'](Ad):
            classifications.append(3)  # 明显畸变
        elif ad_cond['gt_8_le_12'](Ad):
            classifications.append(4)  # 严重畸变
        else:
            classifications.append(4)  # 超出范围，视为严重畸变

    # Energy% 分类
    if enabled_indicators.get('energy', False) and Energy is not None and energy_cond:
        if energy_cond['normal'](Energy):
            classifications.append(1)  # 正常
        elif energy_cond['light'](Energy):
            classifications.append(2)  # 轻微畸变
        elif energy_cond['obvious'](Energy):
            classifications.append(3)  # 明显畸变
        elif energy_cond['severe'](Energy):
            classifications.append(4)  # 严重畸变
        else:
            classifications.append(4)  # 超出范围，视为严重畸变

    # PSD 分类
    if enabled_indicators.get('psd', False) and PSD is not None and psd_cond:
        if psd_cond['normal'](PSD):
            classifications.append(1)  # 正常
        elif psd_cond['light'](PSD):
            classifications.append(2)  # 轻微畸变
        elif psd_cond['obvious'](PSD):
            classifications.append(3)  # 明显畸变
        elif psd_cond['severe'](PSD):
            classifications.append(4)  # 严重畸变
        else:
            classifications.append(4)  # 超出范围，视为严重畸变

    # 如果没有启用任何指标，返回1（正常）
    if not classifications:
        return 1

    # 取最严重的分类作为最终K值
    return max(classifications)

def calculate_I_ji(Sp, Ad, Bi_ratio, config=DEFAULT_GZ_CONFIG):
    sp_cond = config['Sp_conditions']
    ad_cond = config['Ad_conditions']
    br_cond = config['Bi_ratio_conditions']
    if br_cond['gt_08'](Bi_ratio):
        if sp_cond['ge_100'](Sp) and ad_cond['le_0'](Ad): return 1
        if sp_cond['85_lt_100'](Sp) and ad_cond['le_0'](Ad): return 1
        if sp_cond['ge_100'](Sp) and ad_cond['gt_0_le_4'](Ad): return 1
    if br_cond['gt_05_le_08'](Bi_ratio) and \
       sp_cond['85_lt_100'](Sp) and ad_cond['gt_0_le_4'](Ad): return 2
    if br_cond['gt_05'](Bi_ratio):
        if sp_cond['75_lt_85'](Sp) and ad_cond['le_4'](Ad): return 2
        if sp_cond['ge_85'](Sp) and ad_cond['gt_4_le_8'](Ad): return 2
    if br_cond['gt_025_le_05'](Bi_ratio) and \
       sp_cond['75_lt_85'](Sp) and ad_cond['gt_4_le_8'](Ad): return 3
    if br_cond['gt_025'](Bi_ratio):
        if sp_cond['65_lt_75'](Sp) and ad_cond['le_8'](Ad): return 3
        if sp_cond['ge_75'](Sp) and ad_cond['gt_8_le_12'](Ad): return 3
    if br_cond['le_025'](Bi_ratio):
        if sp_cond['65_lt_75'](Sp) and ad_cond['gt_8_le_12'](Ad): return 4
        if sp_cond['lt_65'](Sp) and ad_cond['le_12'](Ad): return 4
        if sp_cond['ge_65'](Sp) and ad_cond['gt_12'](Ad): return 4
    if br_cond['gt_08'](Bi_ratio):
        if (sp_cond['75_lt_85'](Sp) and ad_cond['le_4'](Ad)) or \
           (sp_cond['ge_85'](Sp) and ad_cond['gt_4_le_8'](Ad)): return 2
        if (sp_cond['65_lt_75'](Sp) and ad_cond['le_8'](Ad)) or \
           (sp_cond['ge_75'](Sp) and ad_cond['gt_8_le_12'](Ad)): return 3
        if (sp_cond['lt_65'](Sp)) or (ad_cond['gt_12'](Ad)): return 4
    if Bi_ratio <= 0.25: return 4
    if 0.25 < Bi_ratio <= 0.5: return 3
    if 0.5 < Bi_ratio <= 0.8: return 2
    return 2

def calculate_K_i(I_ji_values_at_depth):
    if not I_ji_values_at_depth: return 0
    valid_I_ji = [i_val for i_val in I_ji_values_at_depth if i_val in [1, 2, 3, 4]]
    if not valid_I_ji: return 0
    sum_I_ji_sq = sum(i_val**2 for i_val in valid_I_ji)
    sum_I_ji = sum(valid_I_ji)
    if sum_I_ji == 0: return 0
    K_i_float = (sum_I_ji_sq / sum_I_ji) + 0.5
    return int(K_i_float)

def check_consecutive_K(K_values_with_depths, target_K, num_consecutive=6, depth_interval=0.1):
    if num_consecutive <= 0: return False, -1
    sorted_k_data = sorted(K_values_with_depths.items())
    if len(sorted_k_data) < num_consecutive: return False, -1
    for i in range(len(sorted_k_data) - num_consecutive + 1):
        window = sorted_k_data[i : i + num_consecutive]
        all_target_K = all(item[1] == target_K for item in window)
        if not all_target_K: continue
        start_depth_of_window = window[0][0]
        end_depth_of_window = window[-1][0]
        actual_span = end_depth_of_window - start_depth_of_window
        expected_span = (num_consecutive - 1) * depth_interval
        if abs(actual_span - expected_span) < (depth_interval / 2.0):
            return True, start_depth_of_window
    return False, -1

# --- Enhanced Analysis Functions ---
def classify_point_by_indicators(speed, amp, energy, psd, config=DEFAULT_ENHANCED_CONFIG, enabled_indicators=None):
    """
    根据Speed%, Amp%, energy%, PSD指标对测量点进行分类
    返回: '正常', '轻微畸变', '明显畸变', '严重畸变'
    """
    if enabled_indicators is None:
        enabled_indicators = config['enabled_indicators']

    classifications = []

    # Speed% 分类
    if enabled_indicators.get('speed', True) and not pd.isna(speed):
        for category, (min_val, max_val) in config['speed_thresholds'].items():
            if min_val <= speed < max_val:
                classifications.append(category)
                break

    # Amp% 分类
    if enabled_indicators.get('amp', True) and not pd.isna(amp):
        for category, (min_val, max_val) in config['amp_thresholds'].items():
            if min_val <= amp < max_val:
                classifications.append(category)
                break

    # Energy% 分类
    if enabled_indicators.get('energy', True) and not pd.isna(energy):
        for category, (min_val, max_val) in config['energy_thresholds'].items():
            if min_val <= energy < max_val:
                classifications.append(category)
                break

    # PSD 分类
    if enabled_indicators.get('psd', True) and not pd.isna(psd):
        for category, (min_val, max_val) in config['psd_thresholds'].items():
            if min_val <= psd < max_val:
                classifications.append(category)
                break

    # 如果没有分类结果，默认为正常
    if not classifications:
        return '正常'

    # 取最严重的分类
    severity_order = ['正常', '轻微畸变', '明显畸变', '严重畸变']
    max_severity_index = max(severity_order.index(c) for c in classifications)
    return severity_order[max_severity_index]

def analyze_continuity(depth_classifications, continuous_threshold=0.5, depth_interval=0.1):
    """
    分析异常点的连续性
    返回: {depth: {'classification': str, 'continuous_length': float, 'is_continuous': bool}}
    """
    if not depth_classifications:
        return {}

    sorted_depths = sorted(depth_classifications.keys())
    continuity_analysis = {}

    for depth in sorted_depths:
        classification = depth_classifications[depth]
        continuity_analysis[depth] = {
            'classification': classification,
            'continuous_length': 0.0,
            'is_continuous': False
        }

        if classification == '正常':
            continue

        # 计算连续长度
        continuous_length = depth_interval  # 当前点的长度

        # 向前查找连续点
        current_depth = depth
        while True:
            prev_depth = current_depth - depth_interval
            if (prev_depth in depth_classifications and
                depth_classifications[prev_depth] == classification):
                continuous_length += depth_interval
                current_depth = prev_depth
            else:
                break

        # 向后查找连续点
        current_depth = depth
        while True:
            next_depth = current_depth + depth_interval
            if (next_depth in depth_classifications and
                depth_classifications[next_depth] == classification):
                continuous_length += depth_interval
                current_depth = next_depth
            else:
                break

        continuity_analysis[depth]['continuous_length'] = continuous_length
        continuity_analysis[depth]['is_continuous'] = continuous_length > continuous_threshold

    return continuity_analysis

def calculate_lateral_defect_ratio(depth_classifications_by_profile, total_profiles=3):
    """
    计算横向缺陷比例
    depth_classifications_by_profile: {depth: {profile: classification}}
    返回: {depth: {classification: ratio}}
    """
    lateral_ratios = {}

    for depth, profile_classifications in depth_classifications_by_profile.items():
        classification_counts = {}
        for classification in profile_classifications.values():
            classification_counts[classification] = classification_counts.get(classification, 0) + 1

        depth_ratios = {}
        for classification, count in classification_counts.items():
            depth_ratios[classification] = count / total_profiles

        lateral_ratios[depth] = depth_ratios

    return lateral_ratios

def determine_pile_category_enhanced(depth_classifications, continuity_analysis, lateral_ratios,
                                   continuous_threshold=0.5):
    """
    基于增强分析系统的桩类判定（简化版，仅基于连续长度阈值）
    """
    if not depth_classifications:
        return "N/A", ["没有分析数据。"]

    report_details = []

    # 检查每个深度的最严重分类
    all_classifications = list(depth_classifications.values())
    has_severe = any(c == '严重畸变' for c in all_classifications)
    has_obvious = any(c == '明显畸变' for c in all_classifications)
    has_light = any(c == '轻微畸变' for c in all_classifications)

    # IV类桩判定：存在严重畸变且连续长度>阈值
    if has_severe:
        for depth, analysis in continuity_analysis.items():
            if (analysis['classification'] == '严重畸变' and analysis['is_continuous']):
                report_details.append(f"深度{depth:.2f}m存在严重畸变，连续长度{analysis['continuous_length']:.2f}m > {continuous_threshold}m")
                return "IV类桩", report_details

        # 严重畸变但不满足连续性条件
        report_details.append("存在严重畸变但连续长度≤阈值")
        return "III类桩", report_details

    # III类桩判定：存在明显畸变且连续长度>阈值
    if has_obvious:
        for depth, analysis in continuity_analysis.items():
            if (analysis['classification'] == '明显畸变' and analysis['is_continuous']):
                report_details.append(f"深度{depth:.2f}m存在明显畸变，连续长度{analysis['continuous_length']:.2f}m > {continuous_threshold}m")
                return "III类桩", report_details

        # 明显畸变但不满足连续性条件
        report_details.append("存在明显畸变但连续长度≤阈值")
        return "II类桩", report_details

    # II类桩判定：存在局部轻微畸变且连续长度>阈值
    if has_light:
        for depth, analysis in continuity_analysis.items():
            if (analysis['classification'] == '轻微畸变' and analysis['is_continuous']):
                report_details.append(f"深度{depth:.2f}m存在轻微畸变，连续长度{analysis['continuous_length']:.2f}m > {continuous_threshold}m")
                return "II类桩", report_details

        # 轻微畸变但不满足连续性条件
        report_details.append("存在轻微畸变但连续长度≤阈值")
        return "I类桩", report_details

    # I类桩：桩身完整，各指标均正常
    report_details.append("桩身完整，各检测指标均正常")
    return "I类桩", report_details

def determine_final_category(K_values_map_with_depths):
    report_details = []
    if not K_values_map_with_depths:
        return "N/A", ["没有计算K值。"]
    K_values_list = list(K_values_map_with_depths.values())
    has_K4 = any(k == 4 for k in K_values_list)
    has_K3 = any(k == 3 for k in K_values_list)
    has_K2 = any(k == 2 for k in K_values_list)

    if has_K4:
        report_details.append("桩身存在K(i)=4的检测横截面。")
        return "IV类桩", report_details
    consecutive_K3_found, k3_start_depth = check_consecutive_K(K_values_map_with_depths, target_K=3, num_consecutive=6)
    if consecutive_K3_found:
        report_details.append(f"在深度 {k3_start_depth:.2f}m 开始的约50cm范围内K(i)值均为3。")
        return "IV类桩", report_details
    if has_K3:
        num_K3 = K_values_list.count(3)
        if num_K3 == 1:
            report_details.append("所有检测截面仅存在一个K(i)=3的情况，且不存在Ki=4。")
            return "III类桩", report_details
        if num_K3 > 1:
            k3_depths = sorted([d for d, k in K_values_map_with_depths.items() if k == 3])
            adjacent_k3_too_close = False
            for i in range(len(k3_depths) - 1):
                if (k3_depths[i+1] - k3_depths[i]) < 0.5:
                    adjacent_k3_too_close = True
                    report_details.append(f"存在相邻K(i)=3的截面距离小于50cm (例如深度 {k3_depths[i]:.2f}m 和 {k3_depths[i+1]:.2f}m)。")
                    break
            if not adjacent_k3_too_close:
                 report_details.append("所有检测截面存在多个K(i)=3，无Ki=4，且任意两个相邻Ki=3截面距离≥50cm。")
                 return "III类桩", report_details
            else:
                 report_details.append("存在多个K(i)=3，无Ki=4，但部分相邻Ki=3截面距离<50cm (未形成IV类条件)。")
                 return "III类桩", report_details
        report_details.append("桩身存在K(i)=3的检测横截面 (未满足IV类条件，且不符合特定III类细则)。")
        return "III类桩", report_details
    
    consecutive_K2_found, k2_start_depth = check_consecutive_K(K_values_map_with_depths, target_K=2, num_consecutive=6)
    if consecutive_K2_found and not has_K3 and not has_K4:
        report_details.append(f"在深度 {k2_start_depth:.2f}m 开始的约50cm范围内K(i)值均为2，且无Ki=3, Ki=4。")
        return "III类桩", report_details
        
    if has_K2 and not has_K3 and not has_K4:
        num_K2 = K_values_list.count(2)
        if num_K2 == 1:
            report_details.append("所有检测截面仅存在一个K(i)=2，且无Ki=3, Ki=4。")
            return "II类桩", report_details
        if num_K2 > 1:
            report_details.append("所有检测截面存在多个K(i)=2，无Ki=3, Ki=4，且不存在某深度50cm范围内K(i)值均为2。")
            return "II类桩", report_details
            
    if all(k == 1 for k in K_values_list):
        report_details.append("桩身各检测横截面完整性类别指数K(i)均为1。")
        return "I类桩", report_details
    
    if has_K3:
        report_details.append("桩身存在K(i)=3，未满足IV类或特定III类条件 (回退逻辑)。")
        return "III类桩", report_details
    if has_K2:
        report_details.append("桩身存在K(i)=2，未满足III类或特定II类条件 (回退逻辑)。")
        return "II类桩", report_details

    report_details.append("未能明确分类，或数据不满足任何明确的I-IV类桩条件。请检查K值分布。")
    return "未定类别", report_details

# --- Built-in AI Analysis Engine ---
class BuiltInAIAnalyzer:
    def __init__(self, config=None):
        self.config = config or {
            '正常': {'speed': (90.0, 1000.0), 'amp': (-100, 3)},
            '轻微畸变': {'speed': (80.0, 90.0), 'amp': (3, 6)},
            '明显畸变': {'speed': (70.0, 80.0), 'amp': (6, 12)},
            '严重畸变': {'speed': (0.0, 70.0), 'amp': (12, 100)},
            'continuous_threshold': 0.5
        }
        self.classifier_model = None
        self.anomaly_detector = None
        self.scaler = None
        self.feature_importance = {}
        self.training_data = []
        self.feature_extractor = None 
        self.feature_selector = None  
        self.preprocessor = None      
        self.is_optimized_model = False 
        self.loaded_model_path = None 

        self._initialize_models()
        self._initial_training()

        self.ai_output_to_roman_map = {
            0: 'I', 1: 'II', 2: 'III', 3: 'IV',
            '0': 'I', '1': 'II', '2': 'III', '3': 'IV',
            'I类桩': 'I', 'II类桩': 'II', 'III类桩': 'III', 'IV类桩': 'IV',
            'I': 'I', 'II': 'II', 'III': 'III', 'IV': 'IV'
        }
        self.roman_to_chinese_display_map = {
            'I': 'I类桩', 'II': 'II类桩', 'III': 'III类桩', 'IV': 'IV类桩',
            'N/A': 'N/A' # Ensure N/A is handled for display
        }

    def _get_standard_key_for_map(self, key_from_model):
        """Converts various model output types to standard Python int or str for map lookup."""
        if isinstance(key_from_model, (np.integer, np.floating)): # Handles numpy numeric types
            return int(key_from_model)
        # Check for np.str_ (NumPy 2.0+) and older np.string_ for broader compatibility if needed.
        # For NumPy < 2.0, np.str_ is an alias for Python's str, so checking for Python str covers it.
        # For NumPy >= 2.0, np.str_ is the new way for fixed-width Unicode strings.
        # np.unicode_ was removed.
        if hasattr(np, 'str_') and isinstance(key_from_model, np.str_): # For NumPy >= 2.0
             return str(key_from_model)
        if hasattr(np, 'string_') and isinstance(key_from_model, np.string_): # For older NumPy byte strings
             return key_from_model.decode('utf-8', errors='ignore') # Attempt to decode
        # For standard Python types or others, try direct or convert to string as a fallback
        if isinstance(key_from_model, (int, str)):
            return key_from_model
        return str(key_from_model) # Fallback for other types

    def _initialize_models(self):
        try:
            # 确保使用sklearn模型，避免PyTorch模型冲突
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.ensemble import IsolationForest
            from sklearn.preprocessing import StandardScaler

            self.classifier_model = RandomForestClassifier(n_estimators=100, max_depth=10, random_state=42, class_weight='balanced')
            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)
            self.scaler = StandardScaler()
            print("AI models initialized successfully")
        except Exception as e:
            print(f"Failed to initialize AI models: {str(e)}")
            traceback.print_exc()

    def _initial_training(self):
        try:
            print("Performing initial AI model training...")
            success = self.train_models()
            if success:
                print("Initial AI model training completed")
            else:
                print("Initial AI model training failed, will train on first use")
        except Exception as e:
            print(f"Initial training error: {str(e)}, will train on first use")

    def _standardize_column_names(self, df_orig):
        df = df_orig.copy()
        column_mapping = {
            'Depth(m)': 'Depth', '1-2 Speed%': 'S1', '1-2 Amp%': 'A1',
            '1-3 Speed%': 'S2', '1-3 Amp%': 'A2', '2-3 Speed%': 'S3', '2-3 Amp%': 'A3'
        }
        df.rename(columns=column_mapping, inplace=True)
        return df

    def extract_features(self, data_df_orig):
        try:
            data_df = self._standardize_column_names(data_df_orig.copy())
            if hasattr(self, 'is_optimized_model') and self.is_optimized_model and \
               hasattr(self, 'feature_extractor') and self.feature_extractor is not None:
                print("[CONFIG] Using model's specific feature_extractor.")
                if hasattr(self.feature_extractor, 'extract_features') and callable(self.feature_extractor.extract_features):
                    extracted_features, feature_names = self.feature_extractor.extract_features(data_df)
                    if extracted_features.ndim == 1:
                        extracted_features = extracted_features.reshape(1, -1)
                    print(f"[SUCCESS] Model's feature_extractor produced: {extracted_features.shape[1]} features.")
                    return extracted_features, feature_names
                elif hasattr(self.feature_extractor, 'transform') and callable(self.feature_extractor.transform):
                    print("[CONFIG] Model's feature_extractor is a transformer. Applying transform.")
                    extracted_features = self.feature_extractor.transform(data_df)
                    if extracted_features.ndim == 1:
                        extracted_features = extracted_features.reshape(1, -1)
                    feature_names = [f"feature_{i}" for i in range(extracted_features.shape[1])]
                    if hasattr(self.feature_extractor, 'get_feature_names_out'):
                        try:
                            feature_names = self.feature_extractor.get_feature_names_out()
                        except: pass
                    elif hasattr(self.feature_extractor, 'feature_names'):
                        feature_names = self.feature_extractor.feature_names
                    print(f"[SUCCESS] Model's transformer feature_extractor produced: {extracted_features.shape[1]} features.")
                    return extracted_features, feature_names
                else:
                    print(f"[WARNING] Model's feature_extractor does not have a recognized interface. Falling back to standard extraction.")

            # 检测模型期望的特征数量以确定使用哪种特征提取方法
            expected_features = self._get_expected_feature_count()
            print(f"[CONFIG] Model expects {expected_features} features")

            if expected_features <= 54:
                # 使用传统的54特征提取方法（向后兼容）
                return self._extract_legacy_features(data_df)
            else:
                # 使用新的105特征提取方法
                return self._extract_enhanced_features(data_df)
        except Exception as e:
            print(f"[ERROR] Feature extraction error: {str(e)}")
            traceback.print_exc()
            # 回退到传统特征提取
            try:
                return self._extract_legacy_features(data_df_orig)
            except:
                return np.array([]).reshape(1, -1), []

    def _get_expected_feature_count(self):
        """获取模型期望的特征数量"""
        if self.classifier_model is None:
            return 54  # 默认使用传统的54个特征

        # 尝试从模型中获取特征数量
        if hasattr(self.classifier_model, 'n_features_in_'):
            return self.classifier_model.n_features_in_
        elif hasattr(self.classifier_model, 'feature_importances_'):
            return len(self.classifier_model.feature_importances_)
        elif hasattr(self.classifier_model, 'coef_'):
            if len(self.classifier_model.coef_.shape) == 2:
                return self.classifier_model.coef_.shape[1]
            else:
                return len(self.classifier_model.coef_)
        else:
            # 默认返回54个特征（传统模式）
            return 54

    def _extract_legacy_features(self, data_df):
        """传统的54特征提取方法（向后兼容）"""
        print("[CONFIG] Using legacy GUI feature extractor (54 features).")
        features = []
        feature_names = []

        # 基本统计特征 - 仅Speed%和Amp%
        for col in ['S1', 'S2', 'S3', 'A1', 'A2', 'A3']:
            if col in data_df.columns:
                values = data_df[col].dropna()
                if len(values) > 0:
                    features.extend([values.mean(), values.std(), values.min(), values.max(), values.median()])
                    feature_names.extend([f'{col}_mean', f'{col}_std', f'{col}_min', f'{col}_max', f'{col}_median'])

        # Speed analysis
        speed_cols = ['S1', 'S2', 'S3']
        speed_data = data_df[[col for col in speed_cols if col in data_df.columns]]
        if not speed_data.empty:
            avg_speed = speed_data.mean(axis=1)
            features.extend([
                avg_speed.mean(), avg_speed.std(),
                (avg_speed < 70).sum() / len(avg_speed) if len(avg_speed) > 0 else 0,
                (avg_speed < 80).sum() / len(avg_speed) if len(avg_speed) > 0 else 0,
                (avg_speed < 90).sum() / len(avg_speed) if len(avg_speed) > 0 else 0
            ])
            feature_names.extend(['avg_speed_mean', 'avg_speed_std', 'severe_speed_ratio', 'obvious_speed_ratio', 'light_speed_ratio'])
        else:
            features.extend([100.0, 0.0, 0.0, 0.0, 0.0])
            feature_names.extend(['avg_speed_mean', 'avg_speed_std', 'severe_speed_ratio', 'obvious_speed_ratio', 'light_speed_ratio'])

        # Amplitude analysis
        amp_cols = ['A1', 'A2', 'A3']
        amp_data = data_df[[col for col in amp_cols if col in data_df.columns]]
        if not amp_data.empty:
            avg_amp = amp_data.mean(axis=1)
            features.extend([
                avg_amp.mean(), avg_amp.std(),
                (avg_amp > 12).sum() / len(avg_amp) if len(avg_amp) > 0 else 0,
                (avg_amp > 8).sum() / len(avg_amp) if len(avg_amp) > 0 else 0,
                (avg_amp > 6).sum() / len(avg_amp) if len(avg_amp) > 0 else 0
            ])
            feature_names.extend(['avg_amp_mean', 'avg_amp_std', 'severe_amp_ratio', 'obvious_amp_ratio', 'light_amp_ratio'])
        else:
            features.extend([0.0, 0.0, 0.0, 0.0, 0.0])
            feature_names.extend(['avg_amp_mean', 'avg_amp_std', 'severe_amp_ratio', 'obvious_amp_ratio', 'light_amp_ratio'])

        # 深度特征
        if 'Depth' in data_df.columns and not data_df['Depth'].empty:
            depth_range = data_df['Depth'].max() - data_df['Depth'].min()
            features.extend([data_df['Depth'].min(), data_df['Depth'].max(), depth_range, len(data_df)])
            feature_names.extend(['depth_min', 'depth_max', 'depth_range', 'num_points'])
        else:
            features.extend([0.0, 10.0, 10.0, 10])
            feature_names.extend(['depth_min', 'depth_max', 'depth_range', 'num_points'])

        # 相关性特征（仅Speed vs Amp）
        for speed_col in ['S1', 'S2', 'S3']:
            for amp_col in ['A1', 'A2', 'A3']:
                if speed_col in data_df.columns and amp_col in data_df.columns:
                    corr = data_df[speed_col].corr(data_df[amp_col])
                    features.append(corr if not np.isnan(corr) else 0.0)
                    feature_names.append(f'{speed_col}_{amp_col}_corr')
                else:
                    features.append(0.0)
                    feature_names.append(f'{speed_col}_{amp_col}_corr')

        # 趋势分析（仅Speed和Amp）
        for col in ['S1', 'S2', 'S3', 'A1', 'A2', 'A3']:
            if col in data_df.columns and 'Depth' in data_df.columns and len(data_df['Depth']) > 1 and len(data_df[col]) > 1:
                try:
                    valid_indices = data_df['Depth'].notna() & data_df[col].notna()
                    if sum(valid_indices) > 1:
                         slope = np.polyfit(data_df.loc[valid_indices, 'Depth'], data_df.loc[valid_indices, col], 1)[0]
                         features.append(slope)
                    else:
                        features.append(0.0)
                except Exception:
                    features.append(0.0)
                feature_names.append(f'{col}_trend')
            else:
                features.append(0.0)
                feature_names.append(f'{col}_trend')

        # 确保特征数量为54
        target_feature_count = 54
        remaining_features = target_feature_count - len(features)
        if remaining_features > 0:
            features.extend([0.0] * remaining_features)
            feature_names.extend([f'legacy_padding_{i}' for i in range(remaining_features)])

        final_features = np.array(features[:target_feature_count]).reshape(1, -1)
        final_feature_names = feature_names[:target_feature_count]
        print(f"[SUCCESS] Legacy GUI feature_extractor produced: {final_features.shape[1]} features.")
        return final_features, final_feature_names

    def _extract_enhanced_features(self, data_df):
        """新的105特征提取方法（4指标支持）"""
        print("[CONFIG] Using enhanced GUI feature extractor (105 features).")
        features = []
        feature_names = []

        # Basic statistics for all 4 indicators (Speed%, Amp%, Energy%, PSD)
        all_cols = ['S1', 'S2', 'S3', 'A1', 'A2', 'A3', 'E1', 'E2', 'E3', 'P1', 'P2', 'P3']
        for col in all_cols:
            if col in data_df.columns:
                values = data_df[col].dropna()
                if len(values) > 0:
                    features.extend([values.mean(), values.std(), values.min(), values.max(), values.median()])
                    feature_names.extend([f'{col}_mean', f'{col}_std', f'{col}_min', f'{col}_max', f'{col}_median'])
            else:
                # Add default values for missing columns
                if col.startswith('S'):  # Speed columns
                    features.extend([100.0, 0.0, 100.0, 100.0, 100.0])
                elif col.startswith('A'):  # Amplitude columns
                    features.extend([0.0, 0.0, 0.0, 0.0, 0.0])
                elif col.startswith('E'):  # Energy columns
                    features.extend([0.9, 0.0, 0.9, 0.9, 0.9])
                elif col.startswith('P'):  # PSD columns
                    features.extend([0.5, 0.0, 0.5, 0.5, 0.5])
                feature_names.extend([f'{col}_mean', f'{col}_std', f'{col}_min', f'{col}_max', f'{col}_median'])

        # Speed analysis
        speed_cols = ['S1', 'S2', 'S3']
        speed_data = data_df[[col for col in speed_cols if col in data_df.columns]]
        if not speed_data.empty:
            avg_speed = speed_data.mean(axis=1)
            features.extend([
                avg_speed.mean(), avg_speed.std(),
                (avg_speed < 70).sum() / len(avg_speed) if len(avg_speed) > 0 else 0,
                (avg_speed < 80).sum() / len(avg_speed) if len(avg_speed) > 0 else 0,
                (avg_speed < 90).sum() / len(avg_speed) if len(avg_speed) > 0 else 0
            ])
            feature_names.extend(['avg_speed_mean', 'avg_speed_std', 'severe_speed_ratio', 'obvious_speed_ratio', 'light_speed_ratio'])
        else:
            features.extend([100.0, 0.0, 0.0, 0.0, 0.0])
            feature_names.extend(['avg_speed_mean', 'avg_speed_std', 'severe_speed_ratio', 'obvious_speed_ratio', 'light_speed_ratio'])

        # Amplitude analysis
        amp_cols = ['A1', 'A2', 'A3']
        amp_data = data_df[[col for col in amp_cols if col in data_df.columns]]
        if not amp_data.empty:
            avg_amp = amp_data.mean(axis=1)
            features.extend([
                avg_amp.mean(), avg_amp.std(),
                (avg_amp > 12).sum() / len(avg_amp) if len(avg_amp) > 0 else 0,
                (avg_amp > 8).sum() / len(avg_amp) if len(avg_amp) > 0 else 0,
                (avg_amp > 6).sum() / len(avg_amp) if len(avg_amp) > 0 else 0
            ])
            feature_names.extend(['avg_amp_mean', 'avg_amp_std', 'severe_amp_ratio', 'obvious_amp_ratio', 'light_amp_ratio'])
        else:
            features.extend([0.0, 0.0, 0.0, 0.0, 0.0])
            feature_names.extend(['avg_amp_mean', 'avg_amp_std', 'severe_amp_ratio', 'obvious_amp_ratio', 'light_amp_ratio'])

        # Energy analysis
        energy_cols = ['E1', 'E2', 'E3']
        energy_data = data_df[[col for col in energy_cols if col in data_df.columns]]
        if not energy_data.empty:
            avg_energy = energy_data.mean(axis=1)
            features.extend([
                avg_energy.mean(), avg_energy.std(),
                (avg_energy < 0.25).sum() / len(avg_energy) if len(avg_energy) > 0 else 0,
                (avg_energy < 0.5).sum() / len(avg_energy) if len(avg_energy) > 0 else 0,
                (avg_energy < 0.8).sum() / len(avg_energy) if len(avg_energy) > 0 else 0
            ])
            feature_names.extend(['avg_energy_mean', 'avg_energy_std', 'severe_energy_ratio', 'obvious_energy_ratio', 'light_energy_ratio'])
        else:
            features.extend([0.9, 0.0, 0.0, 0.0, 0.0])
            feature_names.extend(['avg_energy_mean', 'avg_energy_std', 'severe_energy_ratio', 'obvious_energy_ratio', 'light_energy_ratio'])

        # PSD analysis
        psd_cols = ['P1', 'P2', 'P3']
        psd_data = data_df[[col for col in psd_cols if col in data_df.columns]]
        if not psd_data.empty:
            avg_psd = psd_data.mean(axis=1)
            features.extend([
                avg_psd.mean(), avg_psd.std(),
                (avg_psd > 3.0).sum() / len(avg_psd) if len(avg_psd) > 0 else 0,
                (avg_psd > 2.0).sum() / len(avg_psd) if len(avg_psd) > 0 else 0,
                (avg_psd > 1.0).sum() / len(avg_psd) if len(avg_psd) > 0 else 0
            ])
            feature_names.extend(['avg_psd_mean', 'avg_psd_std', 'severe_psd_ratio', 'obvious_psd_ratio', 'light_psd_ratio'])
        else:
            features.extend([0.5, 0.0, 0.0, 0.0, 0.0])
            feature_names.extend(['avg_psd_mean', 'avg_psd_std', 'severe_psd_ratio', 'obvious_psd_ratio', 'light_psd_ratio'])

        # 深度特征
        if 'Depth' in data_df.columns and not data_df['Depth'].empty:
            depth_range = data_df['Depth'].max() - data_df['Depth'].min()
            features.extend([data_df['Depth'].min(), data_df['Depth'].max(), depth_range, len(data_df)])
            feature_names.extend(['depth_min', 'depth_max', 'depth_range', 'num_points'])
        else:
            features.extend([0.0, 10.0, 10.0, 10])
            feature_names.extend(['depth_min', 'depth_max', 'depth_range', 'num_points'])

        # 相关性特征（Speed vs Amp）
        for speed_col in ['S1', 'S2', 'S3']:
            for amp_col in ['A1', 'A2', 'A3']:
                if speed_col in data_df.columns and amp_col in data_df.columns:
                    corr = data_df[speed_col].corr(data_df[amp_col])
                    features.append(corr if not np.isnan(corr) else 0.0)
                    feature_names.append(f'{speed_col}_{amp_col}_corr')
                else:
                    features.append(0.0)
                    feature_names.append(f'{speed_col}_{amp_col}_corr')

        # 趋势分析（所有指标）
        all_indicator_cols = ['S1', 'S2', 'S3', 'A1', 'A2', 'A3', 'E1', 'E2', 'E3', 'P1', 'P2', 'P3']
        for col in all_indicator_cols:
            if col in data_df.columns and 'Depth' in data_df.columns and len(data_df['Depth']) > 1 and len(data_df[col]) > 1:
                try:
                    valid_indices = data_df['Depth'].notna() & data_df[col].notna()
                    if sum(valid_indices) > 1:
                         slope = np.polyfit(data_df.loc[valid_indices, 'Depth'], data_df.loc[valid_indices, col], 1)[0]
                         features.append(slope)
                    else:
                        features.append(0.0)
                except Exception:
                    features.append(0.0)
                feature_names.append(f'{col}_trend')
            else:
                features.append(0.0)
                feature_names.append(f'{col}_trend')

        # 确保特征数量为105
        # 12 indicators * 5 stats + 4 indicator groups * 5 stats + 4 depth features + 9 correlations + 12 trends
        target_feature_count = 12*5 + 4*5 + 4 + 9 + 12  # = 60 + 20 + 4 + 9 + 12 = 105
        remaining_features = target_feature_count - len(features)
        if remaining_features > 0:
            features.extend([0.0] * remaining_features)
            feature_names.extend([f'enhanced_padding_{i}' for i in range(remaining_features)])

        final_features = np.array(features[:target_feature_count]).reshape(1, -1)
        final_feature_names = feature_names[:target_feature_count]
        print(f"[SUCCESS] Enhanced GUI feature_extractor produced: {final_features.shape[1]} features.")
        return final_features, final_feature_names

    def generate_synthetic_training_data(self, num_samples=1000):
        try:
            training_features = []
            training_labels = []
            categories = ['I类桩', 'II类桩', 'III类桩', 'IV类桩']
            num_speed_features = 15 
            num_amp_features = 15
            num_other_features = 54 - num_speed_features - num_amp_features

            for category in categories:
                for _ in range(num_samples // len(categories)):
                    if category == 'I类桩':
                        speed_features = np.random.normal(95, 5, num_speed_features)
                        amp_features = np.random.normal(2, 1, num_amp_features)
                    elif category == 'II类桩':
                        speed_features = np.random.normal(85, 5, num_speed_features)
                        amp_features = np.random.normal(5, 2, num_amp_features)
                    elif category == 'III类桩':
                        speed_features = np.random.normal(75, 5, num_speed_features)
                        amp_features = np.random.normal(9, 3, num_amp_features)
                    else:
                        speed_features = np.random.normal(60, 10, num_speed_features)
                        amp_features = np.random.normal(15, 5, num_amp_features)
                    
                    other_features_array = np.random.normal(0, 1, num_other_features)
                    features = np.concatenate([speed_features, amp_features, other_features_array])
                    training_features.append(features)
                    training_labels.append(category)
            
            self.training_data = list(zip(training_features, training_labels))
            print(f"[SUCCESS] Generated {len(training_features)} synthetic training samples with {len(training_features[0])} features each.")
            return np.array(training_features), np.array(training_labels)
        except Exception as e:
            print(f"[ERROR] Synthetic data generation error: {str(e)}")
            return np.array([]), np.array([])

    def train_models(self, features=None, labels=None):
        try:
            if features is None or labels is None or len(features) == 0:
                print("[CONFIG] No explicit training data provided, generating synthetic data for training.")
                features, labels = self.generate_synthetic_training_data()

            if len(features) == 0:
                print("[ERROR] No training data available, cannot train models.")
                return False

            print(f"[TRAIN] Training models with data of shape: {features.shape}")
            
            try:
                features_scaled = self.scaler.fit_transform(features)
            except Exception as scale_fit_error:
                print(f"[ERROR] Error fitting scaler: {scale_fit_error}. Re-initializing scaler.")
                self.scaler = StandardScaler()
                features_scaled = self.scaler.fit_transform(features)

            self.classifier_model.fit(features_scaled, labels)
            self.anomaly_detector.fit(features_scaled)

            if hasattr(self.classifier_model, 'feature_importances_'):
                num_feats = features.shape[1]
                feature_names_for_importance = [f'feature_{i}' for i in range(num_feats)]
                self.feature_importance = dict(zip(feature_names_for_importance, self.classifier_model.feature_importances_))
            
            print("[SUCCESS] AI models trained successfully")
            return True
        except Exception as e:
            print(f"[ERROR] Model training error: {str(e)}")
            traceback.print_exc()
            return False

    def predict(self, data_df): # Common predict method for BuiltInAIAnalyzer
        try:
            print(f"[AI] BuiltInAIAnalyzer.predict called with data_df of shape: {data_df.shape}")
            features, feature_names = self.extract_features(data_df)

            if features.size == 0:
                print("[ERROR] No features extracted from data_df in predict method.")
                return None
            
            print(f"[PROCESS] Features extracted for prediction, shape: {features.shape}")

            if hasattr(self, 'is_optimized_model') and self.is_optimized_model:
                print("[OPTIMIZE] Routing to optimized model prediction logic (within BuiltInAIAnalyzer).")
                return self._predict_with_optimized_model_internal(features, data_df) 
            else:
                print("[CONFIG] Routing to standard model prediction logic (within BuiltInAIAnalyzer).")
                if self.classifier_model is None or not hasattr(self.classifier_model, 'classes_'):
                    print("[WARNING] Standard classifier model not available or not trained. Attempting to train with synthetic data...")
                    if not self.train_models(): 
                        print("[ERROR] Failed to train standard model, cannot predict.")
                        return None
                
                try:
                    if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None: 
                        print("[WARNING] Standard scaler not fitted. This should have been handled by train_models.")
                    features_scaled = self.scaler.transform(features)
                except Exception as scale_error:
                    print(f"[WARNING] Scaler error during standard prediction: {scale_error}. Retraining models as a fallback.")
                    if not self.train_models(): 
                         print("[ERROR] Failed to retrain models after scaler error.")
                         return None
                    features_scaled = self.scaler.transform(features) 

                prediction_from_model_raw = self.classifier_model.predict(features_scaled)[0] 
                probabilities = self.classifier_model.predict_proba(features_scaled)[0]
                class_names_from_model_raw = self.classifier_model.classes_ 

                key_for_pred = self._get_standard_key_for_map(prediction_from_model_raw)
                predicted_category_roman = self.ai_output_to_roman_map.get(key_for_pred)
                if predicted_category_roman is None:
                    predicted_category_roman = 'N/A' # Default for unmappable
                    print(f"[WARNING] Standard AI prediction '{key_for_pred}' (type: {type(key_for_pred)}) not in roman map. Defaulting to 'N/A'.")


                class_probabilities_roman_keys = {}
                for i, class_label_raw_item in enumerate(class_names_from_model_raw):
                    key_for_class = self._get_standard_key_for_map(class_label_raw_item)
                    roman_key = self.ai_output_to_roman_map.get(key_for_class)
                    if roman_key is None: 
                        roman_key = str(key_for_class) 
                        print(f"[WARNING] Standard AI class name '{key_for_class}' (type: {type(key_for_class)}) not in roman map for probabilities. Using raw key: {roman_key}")
                    class_probabilities_roman_keys[roman_key] = probabilities[i]
                
                anomaly_score = self.anomaly_detector.decision_function(features_scaled)[0]
                is_anomaly = self.anomaly_detector.predict(features_scaled)[0] == -1
                
                confidence = class_probabilities_roman_keys.get(predicted_category_roman, 0.0)

                result = {
                    '完整性类别': predicted_category_roman, 
                    'ai_confidence': confidence,
                    'anomaly_score': anomaly_score,
                    'is_anomaly': is_anomaly,
                    'class_probabilities': class_probabilities_roman_keys, 
                    'feature_importance': self.feature_importance, 
                    'overall_reasoning': self._generate_reasoning(predicted_category_roman, confidence, anomaly_score, class_probabilities_roman_keys)
                }
                print(f"[DART] 标准模型预测结果 (罗马数字): {predicted_category_roman} | 置信度: {confidence:.2%}")
                return result
        except Exception as e:
            print(f"[ERROR] Prediction error in main predict method: {str(e)}")
            traceback.print_exc()
            return None

    def _predict_with_optimized_model_internal(self, features, data_df_for_preprocessor=None):
        try:
            print(f"[START] Inside _predict_with_optimized_model_internal with features shape: {features.shape}")
            final_features_for_model = None

            if hasattr(self, 'scaler') and self.scaler and \
               hasattr(self, 'feature_selector') and self.feature_selector:
                print("[START] Applying model's scaler and feature_selector.")
                if not hasattr(self.scaler, 'mean_') or self.scaler.mean_ is None:
                     print("CRITICAL ERROR: Optimized model's scaler is not fitted. Cannot proceed.")
                     return None
                features_scaled = self.scaler.transform(features)
                features_selected = self.feature_selector.transform(features_scaled)
                final_features_for_model = features_selected
            elif hasattr(self, 'preprocessor') and self.preprocessor:
                print("[START] Applying model's preprocessor.")
                final_features_for_model = self.preprocessor.transform(features) 
            elif hasattr(self, 'scaler') and self.scaler:
                print("[START] Applying model's scaler only.")
                if not hasattr(self.scaler, 'mean_') or self.scaler.mean_ is None:
                     print("CRITICAL ERROR: Optimized model's scaler is not fitted. Cannot proceed.")
                     return None
                final_features_for_model = self.scaler.transform(features)
            else:
                print("[START] Using features directly for optimized model (no further scaler/selector/preprocessor found).")
                final_features_for_model = features

            if final_features_for_model is None:
                print("CRITICAL ERROR: final_features_for_model is None before prediction.")
                return None
            if self.classifier_model is None:
                print("CRITICAL ERROR: classifier_model is None for optimized model.")
                return None

            prediction_from_model_raw = self.classifier_model.predict(final_features_for_model)[0]
            probabilities = self.classifier_model.predict_proba(final_features_for_model)[0]
            class_names_from_model_raw = self.classifier_model.classes_

            key_for_pred_opt = self._get_standard_key_for_map(prediction_from_model_raw)
            predicted_category_roman = self.ai_output_to_roman_map.get(key_for_pred_opt)
            if predicted_category_roman is None:
                 predicted_category_roman = 'N/A' 
                 print(f"[WARNING] Optimized AI prediction '{key_for_pred_opt}' (type: {type(key_for_pred_opt)}) not in roman map. Defaulting to 'N/A'.")
            
            class_probabilities_roman_keys = {}
            for i, class_label_raw_item in enumerate(class_names_from_model_raw):
                key_for_class_opt = self._get_standard_key_for_map(class_label_raw_item)
                roman_key = self.ai_output_to_roman_map.get(key_for_class_opt)
                if roman_key is None: 
                    roman_key = str(key_for_class_opt) 
                    print(f"[WARNING] Optimized AI class name '{key_for_class_opt}' (type: {type(key_for_class_opt)}) not in roman map for probabilities. Using raw key: {roman_key}")
                class_probabilities_roman_keys[roman_key] = probabilities[i]
            
            anomaly_score = -0.5 
            is_anomaly = False    
            
            confidence = class_probabilities_roman_keys.get(predicted_category_roman, 0.0)

            current_feature_importance = {}
            if hasattr(self.classifier_model, 'feature_importances_'):
                num_model_features = len(self.classifier_model.feature_importances_)
                if self.feature_importance and len(self.feature_importance) == num_model_features: 
                     current_feature_importance = self.feature_importance
                else: 
                    current_feature_importance = {f"model_feat_{i}": imp for i, imp in enumerate(self.classifier_model.feature_importances_)}
            
            result = {
                '完整性类别': predicted_category_roman, 
                'ai_confidence': confidence,
                'anomaly_score': anomaly_score,
                'is_anomaly': is_anomaly,
                'class_probabilities': class_probabilities_roman_keys, 
                'feature_importance': current_feature_importance, 
                'overall_reasoning': self._generate_reasoning(predicted_category_roman, confidence, anomaly_score, class_probabilities_roman_keys)
            }
            print(f"[DART] Optimized model prediction (罗马数字): {predicted_category_roman} with {confidence:.2%} confidence")
            return result
        except Exception as e:
            print(f"[ERROR] Optimized model internal prediction error: {str(e)}")
            traceback.print_exc()
            return None

    def _generate_reasoning(self, prediction_roman, confidence, anomaly_score, class_probabilities_roman_keys):
        prediction_display = self.roman_to_chinese_display_map.get(prediction_roman, prediction_roman)
        reasoning = f"AI分析结果：{prediction_display}\n\n"
        reasoning += f"置信度分析：\n- 主要预测置信度：{confidence:.2%}\n"
        if confidence > 0.8: reasoning += "- 置信度较高，预测结果可信\n"
        elif confidence > 0.6: reasoning += "- 置信度中等，建议结合传统方法\n"
        else: reasoning += "- 置信度较低，建议以传统方法为准\n"
        reasoning += f"\n异常检测：\n- 异常分数：{anomaly_score:.3f}\n" 
        if anomaly_score < -0.1 and self.anomaly_detector: 
            reasoning += "- 检测到异常模式，需要特别关注\n" 
        else: reasoning += "- 数据模式正常 (或异常检测未启用)\n"
        reasoning += f"\n各类别概率分布：\n"
        sorted_probs = sorted(class_probabilities_roman_keys.items(), key=lambda item: (item[1], item[0]), reverse=True)
        for class_name_roman, prob in sorted_probs:
            class_name_display = self.roman_to_chinese_display_map.get(class_name_roman, class_name_roman)
            reasoning += f"- {class_name_display}：{prob:.2%}\n"
        return reasoning

    def save_models(self, filepath):
        try:
            model_data = {
                'classifier_model': self.classifier_model,
                'anomaly_detector': self.anomaly_detector,
                'scaler': self.scaler,
                'feature_importance': self.feature_importance, 
                'config': self.config,
                'feature_extractor': self.feature_extractor if hasattr(self, 'feature_extractor') else None,
                'feature_selector': self.feature_selector if hasattr(self, 'feature_selector') else None,
                'preprocessor': self.preprocessor if hasattr(self, 'preprocessor') else None,
                'is_optimized_model': self.is_optimized_model
            }
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            print(f"[SUCCESS] Models saved to {filepath}")
            return True
        except Exception as e:
            print(f"[ERROR] Failed to save models: {str(e)}")
            return False

    def load_models(self, filepath):
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            print(f"[PACKAGE] Loaded model_data type: {type(model_data)}")
            if isinstance(model_data, dict):
                print(f"[PACKAGE] model_data keys: {list(model_data.keys())}")

            self.is_optimized_model = False
            self.feature_extractor = None
            self.feature_selector = None
            self.preprocessor = None
            self.scaler = StandardScaler() 
            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42) 
            self.feature_importance = {}

            if isinstance(model_data, dict):
                # 优先查找sklearn分类器，避免加载PyTorch模型
                potential_classifier = model_data.get('classifier_model')
                if potential_classifier is None:
                    potential_classifier = model_data.get('model')

                # 检查是否是sklearn模型
                if potential_classifier and hasattr(potential_classifier, 'fit') and hasattr(potential_classifier, 'predict'):
                    # 确保不是PyTorch模型
                    if not str(type(potential_classifier)).startswith("<class 'torch."):
                        self.classifier_model = potential_classifier
                        print(f"[SUCCESS] 加载sklearn分类器: {type(potential_classifier).__name__}")
                    else:
                        print(f"[WARNING] 检测到PyTorch模型，跳过加载，使用默认sklearn模型")
                        self.classifier_model = RandomForestClassifier(n_estimators=100, max_depth=10, random_state=42, class_weight='balanced')
                else:
                    print(f"[WARNING] 未找到有效的sklearn分类器，使用默认模型")
                    self.classifier_model = RandomForestClassifier(n_estimators=100, max_depth=10, random_state=42, class_weight='balanced')

                loaded_scaler = model_data.get('scaler')
                if loaded_scaler: self.scaler = loaded_scaler
                loaded_anomaly_detector = model_data.get('anomaly_detector')
                if loaded_anomaly_detector: self.anomaly_detector = loaded_anomaly_detector
                self.feature_importance = model_data.get('feature_importance', {})
                self.config = model_data.get('config', self.config)

                if model_data.get('is_optimized_model'): 
                    self.is_optimized_model = True
                    print("[FLAG] is_optimized_model flag found in model file.")
                elif 'feature_extractor' in model_data or 'preprocessor' in model_data : 
                    self.is_optimized_model = True
                    print("[FLAG] Inferred is_optimized_model based on components.")

                if self.is_optimized_model:
                    self.feature_extractor = model_data.get('feature_extractor')
                    self.feature_selector = model_data.get('feature_selector')
                    self.preprocessor = model_data.get('preprocessor')
                    if self.feature_extractor: print("Loaded model's feature_extractor.")
                    if self.feature_selector: print("Loaded model's feature_selector.")
                    if self.preprocessor: print("Loaded model's preprocessor.")
                    if self.preprocessor is None and loaded_scaler is None:
                        print("[WARNING] Optimized model loaded but no specific scaler or preprocessor found.")
                    elif loaded_scaler:
                        print("Loaded model's scaler.")
                if self.classifier_model is None:
                     print(f"[ERROR] Critical: No classifier model found in the .pkl dictionary.")
                     return False
                print(f"[SUCCESS] Model package loaded from {filepath}. is_optimized_model: {self.is_optimized_model}")
            elif hasattr(model_data, 'predict'): 
                self.classifier_model = model_data
                self.is_optimized_model = False 
                print(f"[SUCCESS] Single classifier model loaded from {filepath}. Treating as standard model.")
                self._initialize_missing_components_for_standard_model()
            else:
                print(f"[ERROR] Unknown model format in {filepath}: {type(model_data)}")
                return False
            
            self.loaded_model_path = filepath 
            return True
        except Exception as e:
            print(f"[ERROR] Failed to load models from {filepath}: {str(e)}")
            traceback.print_exc()
            return False

    def _initialize_missing_components_for_standard_model(self):
        try:
            if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None or \
               not hasattr(self.anomaly_detector, 'estimators_features_'): 
                print("[CONFIG] Standard model loaded, components (scaler/anomaly detector) might need fitting. Using synthetic data.")
                features_syn, _ = self.generate_synthetic_training_data(100) 
                if features_syn.size > 0:
                    if not hasattr(self.scaler, 'scale_') or self.scaler.scale_ is None:
                        self.scaler.fit(features_syn)
                        print("[SUCCESS] Scaler fitted with synthetic data for standard model.")
                    if not hasattr(self.anomaly_detector, 'estimators_features_'):
                        scaled_features_syn = self.scaler.transform(features_syn)
                        self.anomaly_detector.fit(scaled_features_syn)
                        print("[SUCCESS] Anomaly detector fitted with synthetic data for standard model.")
        except Exception as e:
            print(f"[WARNING] Error initializing missing components for standard model: {e}")

# --- GUI Class ---
class PileAnalyzerGZGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Pile Integrity Analyzer (GZ Method) - 桩基完整性分析系统 (GZ方法)")
        self.root.geometry("1600x1000")
        self.root.minsize(1200, 800)
        self.root.configure(bg='#f8f9fa')
        self.root.resizable(True, True)
        self.root.state('normal')
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(lambda: self.root.attributes('-topmost', False))
        self.center_window()
        self.setup_window_controls()
        self.setup_styles()

        self.ai_models_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ai_models')
        os.makedirs(self.ai_models_dir, exist_ok=True)
        
        # 优先加载V2系统，移除V1系统
        try:
            from ai_analyzer_v2 import get_ai_analyzer_v2
            self.ai_analyzer_v2 = get_ai_analyzer_v2()
            self.ai_output_to_roman_map = self.ai_analyzer_v2.ai_output_to_roman_map
            self.use_v2_analyzer = True
            print("[SUCCESS] AI分析器V2.0加载成功")

            # 为了向后兼容，保留ai_analyzer引用
            self.ai_analyzer = self.ai_analyzer_v2

            # 设置映射关系
            self.roman_to_chinese_display_map = {
                'I': 'I类桩', 'II': 'II类桩', 'III': 'III类桩', 'IV': 'IV类桩'
            }

        except ImportError as e:
            print(f"[ERROR] AI分析器V2.0加载失败: {e}")
            # 如果V2加载失败，回退到V1
            self.ai_analyzer = BuiltInAIAnalyzer()
            self.roman_to_chinese_display_map = self.ai_analyzer.roman_to_chinese_display_map
            self.ai_output_to_roman_map = self.ai_analyzer.ai_output_to_roman_map
            self.ai_analyzer_v2 = None
            self.use_v2_analyzer = False
            print("[FALLBACK] 回退到AI分析器V1.0")
        except Exception as e:
            print(f"[ERROR] AI分析器V2.0启用失败: {e}")
            # 如果V2加载失败，回退到V1
            self.ai_analyzer = BuiltInAIAnalyzer()
            self.roman_to_chinese_display_map = self.ai_analyzer.roman_to_chinese_display_map
            self.ai_output_to_roman_map = self.ai_analyzer.ai_output_to_roman_map
            self.ai_analyzer_v2 = None
            self.use_v2_analyzer = False
            print("[FALLBACK] 回退到AI分析器V1.0")

        self.current_file = None
        self.analysis_results = {}
        self.progress_queue = queue.Queue()
        self.data_df = None
        self.profiles = []
        self.profile_name_map = {}

        self.config_vars = {
            'sp_ge_100': tk.DoubleVar(value=100.0), 'sp_85_lt_100_min': tk.DoubleVar(value=85.0),
            'sp_85_lt_100_max': tk.DoubleVar(value=100.0), 'sp_75_lt_85_min': tk.DoubleVar(value=75.0),
            'sp_75_lt_85_max': tk.DoubleVar(value=85.0), 'sp_65_lt_75_min': tk.DoubleVar(value=65.0),
            'sp_65_lt_75_max': tk.DoubleVar(value=75.0), 'ad_le_0': tk.DoubleVar(value=0.0),
            'ad_gt_0_le_4_min': tk.DoubleVar(value=0.0), 'ad_gt_0_le_4_max': tk.DoubleVar(value=4.0),
            'ad_gt_4_le_8_min': tk.DoubleVar(value=4.0), 'ad_gt_4_le_8_max': tk.DoubleVar(value=8.0),
            'ad_gt_8_le_12_min': tk.DoubleVar(value=8.0), 'ad_gt_8_le_12_max': tk.DoubleVar(value=12.0),
            'bi_ratio_default': tk.DoubleVar(value=1.0), 'auto_analysis': tk.BooleanVar(value=True),
            'show_details': tk.BooleanVar(value=True), 'ai_model_path': tk.StringVar(value=""),
            # Enhanced analysis configuration
            'enhanced_analysis_enabled': tk.BooleanVar(value=True),
            'continuous_threshold': tk.DoubleVar(value=0.5),
            # GZ Traditional analysis 指标启用控制
            'gz_enable_speed': tk.BooleanVar(value=True),
            'gz_enable_amplitude': tk.BooleanVar(value=True),
            'gz_enable_energy': tk.BooleanVar(value=True),
            'gz_enable_psd': tk.BooleanVar(value=False),

            # GZ method Energy% 阈值配置
            'gz_energy_normal_min': tk.DoubleVar(value=0.8),
            'gz_energy_normal_max': tk.DoubleVar(value=100.0),
            'gz_energy_light_min': tk.DoubleVar(value=0.5),
            'gz_energy_light_max': tk.DoubleVar(value=0.8),
            'gz_energy_obvious_min': tk.DoubleVar(value=0.25),
            'gz_energy_obvious_max': tk.DoubleVar(value=0.5),
            'gz_energy_severe_min': tk.DoubleVar(value=0.0),
            'gz_energy_severe_max': tk.DoubleVar(value=0.25),

            # GZ method PSD 阈值配置
            'gz_psd_normal_min': tk.DoubleVar(value=0.0),
            'gz_psd_normal_max': tk.DoubleVar(value=1.0),
            'gz_psd_light_min': tk.DoubleVar(value=1.0),
            'gz_psd_light_max': tk.DoubleVar(value=2.0),
            'gz_psd_obvious_min': tk.DoubleVar(value=2.0),
            'gz_psd_obvious_max': tk.DoubleVar(value=3.0),
            'gz_psd_severe_min': tk.DoubleVar(value=3.0),
            'gz_psd_severe_max': tk.DoubleVar(value=100.0),

            # GZ method 深度范围配置
            'gz_depth_range': tk.DoubleVar(value=0.5),  # 默认0.5m (50cm)

            # Indicator enable/disable
            'enable_speed': tk.BooleanVar(value=True),
            'enable_amp': tk.BooleanVar(value=True),
            'enable_energy': tk.BooleanVar(value=True),
            'enable_psd': tk.BooleanVar(value=True),
            # Speed thresholds
            'speed_normal_min': tk.DoubleVar(value=100.0), 'speed_normal_max': tk.DoubleVar(value=1000.0),
            'speed_light_min': tk.DoubleVar(value=85.0), 'speed_light_max': tk.DoubleVar(value=100.0),
            'speed_obvious_min': tk.DoubleVar(value=75.0), 'speed_obvious_max': tk.DoubleVar(value=85.0),
            'speed_severe_min': tk.DoubleVar(value=0.0), 'speed_severe_max': tk.DoubleVar(value=75.0),
            # Amp thresholds
            'amp_normal_min': tk.DoubleVar(value=-100), 'amp_normal_max': tk.DoubleVar(value=3),
            'amp_light_min': tk.DoubleVar(value=3), 'amp_light_max': tk.DoubleVar(value=6),
            'amp_obvious_min': tk.DoubleVar(value=6), 'amp_obvious_max': tk.DoubleVar(value=12),
            'amp_severe_min': tk.DoubleVar(value=12), 'amp_severe_max': tk.DoubleVar(value=100),
            # Energy thresholds
            'energy_normal_min': tk.DoubleVar(value=0.8), 'energy_normal_max': tk.DoubleVar(value=100),
            'energy_light_min': tk.DoubleVar(value=0.5), 'energy_light_max': tk.DoubleVar(value=0.8),
            'energy_obvious_min': tk.DoubleVar(value=0.25), 'energy_obvious_max': tk.DoubleVar(value=0.5),
            'energy_severe_min': tk.DoubleVar(value=0), 'energy_severe_max': tk.DoubleVar(value=0.25),
            # PSD thresholds
            'psd_normal_min': tk.DoubleVar(value=0), 'psd_normal_max': tk.DoubleVar(value=1),
            'psd_light_min': tk.DoubleVar(value=1), 'psd_light_max': tk.DoubleVar(value=2),
            'psd_obvious_min': tk.DoubleVar(value=2), 'psd_obvious_max': tk.DoubleVar(value=3),
            'psd_severe_min': tk.DoubleVar(value=3), 'psd_severe_max': tk.DoubleVar(value=100)
        }
        configure_chinese_fonts()
        self.setup_gui()

    def center_window(self):
        self.root.update_idletasks()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = 1600; window_height = 1000
        if window_width > screen_width: window_width = screen_width - 100
        if window_height > screen_height: window_height = screen_height - 100
        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def bind_mousewheel(self, widget):
        def _on_mousewheel(event): widget.yview_scroll(int(-1*(event.delta/120)), "units")
        def _bind_to_mousewheel(event): widget.bind_all("<MouseWheel>", _on_mousewheel)
        def _unbind_from_mousewheel(event): widget.unbind_all("<MouseWheel>")
        widget.bind('<Enter>', _bind_to_mousewheel)
        widget.bind('<Leave>', _unbind_from_mousewheel)

    def setup_window_controls(self):
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.root.bind('<Escape>', self.exit_fullscreen)
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.is_fullscreen = False

    def toggle_fullscreen(self, event=None):
        self.is_fullscreen = not self.is_fullscreen
        self.root.attributes('-fullscreen', self.is_fullscreen)

    def exit_fullscreen(self, event=None):
        self.is_fullscreen = False
        self.root.attributes('-fullscreen', False)

    def on_closing(self):
        if messagebox.askokcancel("Quit", "Do you want to quit the application?"):
            self.root.quit()
            self.root.destroy()

    def setup_styles(self):
        style = ttk.Style(); style.theme_use('clam')
        self.colors = {'primary': '#2c3e50', 'secondary': '#3498db', 'success': '#27ae60', 
                       'warning': '#f39c12', 'danger': '#e74c3c', 'light': '#ecf0f1', 
                       'dark': '#34495e', 'white': '#ffffff', 'accent': '#9b59b6'}
        style.configure('Title.TLabel', font=('Segoe UI', 18, 'bold'), foreground=self.colors['primary'], background='#f8f9fa')
        style.configure('Heading.TLabel', font=('Segoe UI', 12, 'bold'), foreground=self.colors['dark'], background='#f8f9fa')
        style.configure('Modern.TButton', font=('Segoe UI', 10), padding=(12, 8))
        style.configure('Accent.TButton', font=('Segoe UI', 11, 'bold'), padding=(15, 10))
        style.configure('Success.TButton', font=('Segoe UI', 10, 'bold'), padding=(12, 8))
        style.configure('Modern.TFrame', background='#f8f9fa', relief='flat')
        style.configure('Modern.TNotebook', background='#f8f9fa', borderwidth=0)
        style.configure('Modern.TNotebook.Tab', padding=(20, 12), font=('Segoe UI', 10, 'bold'))

    def create_header(self):
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        header_frame.pack(fill='x', padx=0, pady=0); header_frame.pack_propagate(False)
        tk.Label(header_frame, text="[ANALYSIS] Pile Integrity Analyzer (GZ Method)", font=('Segoe UI', 16, 'bold'), fg='white', bg=self.colors['primary']).pack(pady=(15, 2))
        tk.Label(header_frame, text="桩基完整性分析系统 (GZ方法) | Professional Engineering Analysis Solution", font=('Segoe UI', 9), fg=self.colors['light'], bg=self.colors['primary']).pack()

    def setup_gui(self):
        self.create_header()
        self.notebook = ttk.Notebook(self.root, style='Modern.TNotebook')
        self.notebook.pack(fill='both', expand=True, padx=15, pady=(5, 10))
        self.setup_data_tab()
        self.setup_analysis_tab()
        self.setup_traditional_results_tab()
        self.setup_enhanced_results_tab()
        self.setup_ai_results_tab()
        self.setup_comparison_tab()
        self.setup_visualization_tab()
        self.setup_config_tab()
        self.setup_status_bar()
        self.monitor_progress()

        # Add 3D visualization menu if Plotly is available
        if PLOTLY_AVAILABLE:
            self.setup_3d_visualization_menu()

    def setup_status_bar(self):
        status_frame = tk.Frame(self.root, bg='#e9ecef', height=30)
        status_frame.pack(side='bottom', fill='x'); status_frame.pack_propagate(False)
        self.main_status_var = tk.StringVar(value="Ready")
        tk.Label(status_frame, textvariable=self.main_status_var, bg='#e9ecef', fg='#495057', font=('Segoe UI', 9), anchor='w').pack(side='left', padx=10, pady=5)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, mode='determinate', length=200)
        self.progress_bar.pack(side='right', padx=10, pady=5)

    def monitor_progress(self):
        try:
            while True:
                message = self.progress_queue.get_nowait()
                if isinstance(message, dict):
                    if 'status' in message: self.main_status_var.set(message['status'])
                    if 'progress' in message: self.progress_var.set(message['progress'])
                else: self.main_status_var.set(str(message))
        except queue.Empty: pass
        finally: self.root.after(100, self.monitor_progress)

    def setup_data_tab(self):
        data_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(data_frame, text="Data Loading")
        main_container = ttk.Frame(data_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="Data Loading & Preview", style='Title.TLabel').pack(pady=(0, 20))
        file_frame = ttk.LabelFrame(main_container, text="📂 File Selection")
        file_frame.pack(fill='x', pady=(0, 20), padx=10)
        path_frame = ttk.Frame(file_frame); path_frame.pack(fill='x', padx=15, pady=15)
        ttk.Label(path_frame, text="Selected File:", style='Heading.TLabel').pack(anchor='w')
        self.file_path_var = tk.StringVar(value="No file selected")
        file_display = tk.Frame(path_frame, bg='white', relief='solid', bd=1)
        file_display.pack(fill='x', pady=5)
        self.file_label = tk.Label(file_display, textvariable=self.file_path_var, bg='white', fg='#666666', font=('Segoe UI', 9), anchor='w')
        self.file_label.pack(fill='both', expand=True, padx=10, pady=8)
        button_frame = ttk.Frame(file_frame); button_frame.pack(fill='x', padx=15, pady=(0, 15))
        ttk.Button(button_frame, text="📂 Select Data File", style='Accent.TButton', command=self.select_file).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="[REFRESH] Reload File", style='Modern.TButton', command=self.reload_file).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="[DATA] Quick Analysis", style='Success.TButton', command=self.quick_analysis).pack(side='right')
        preview_frame = ttk.LabelFrame(main_container, text="[DATA] Data Preview")
        preview_frame.pack(fill='both', expand=True, padx=10)
        self.create_data_preview(preview_frame)

    def create_data_preview(self, parent):
        columns = ('Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3', 'E1', 'E2', 'E3', 'P1', 'P2', 'P3')
        column_names = {
            'Depth': 'Depth(m)', 'S1': 'Speed1-2%', 'A1': 'Amp1-2%', 'S2': 'Speed1-3%',
            'A2': 'Amp1-3%', 'S3': 'Speed2-3%', 'A3': 'Amp2-3%', 'E1': 'Energy1-2%',
            'E2': 'Energy1-3%', 'E3': 'Energy2-3%', 'P1': 'PSD1-2', 'P2': 'PSD1-3', 'P3': 'PSD2-3'
        }
        self.data_tree = ttk.Treeview(parent, columns=columns, show='headings', height=12)
        for col in columns:
            self.data_tree.heading(col, text=column_names.get(col, col))
            self.data_tree.column(col, width=80, anchor='center')
        v_scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient='horizontal', command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        self.data_tree.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        v_scrollbar.pack(side='right', fill='y', pady=15)
        h_scrollbar.pack(side='bottom', fill='x', padx=15)
        self.bind_mousewheel(self.data_tree)
        info_frame = ttk.Frame(parent); info_frame.pack(side='bottom', fill='x', padx=15, pady=(0, 15))
        self.data_info_var = tk.StringVar(value="No data loaded")
        ttk.Label(info_frame, textvariable=self.data_info_var, font=('Segoe UI', 10)).pack(anchor='w')

    def setup_analysis_tab(self):
        analysis_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(analysis_frame, text="Analysis")
        main_container = ttk.Frame(analysis_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="Pile Integrity Analysis (GZ Method)", style='Title.TLabel').pack(pady=(0, 20))



        system_selection_frame = ttk.LabelFrame(main_container, text="AI System Selection")
        system_selection_frame.pack(fill='x', pady=(0, 15), padx=10)
        system_frame = ttk.Frame(system_selection_frame); system_frame.pack(fill='x', padx=15, pady=15)
        self.ai_system_var = tk.StringVar(value="v2" if self.use_v2_analyzer else "v1")
        ttk.Radiobutton(system_frame, text="AI System V2.0 (推荐) - 支持多模型管理和高精度分析", variable=self.ai_system_var, value="v2", command=self.switch_ai_system).pack(anchor='w', pady=2)
        ttk.Radiobutton(system_frame, text="AI System V1.0 (兼容) - 传统单模型系统", variable=self.ai_system_var, value="v1", command=self.switch_ai_system).pack(anchor='w', pady=2)

        self.v2_model_frame = ttk.LabelFrame(main_container, text="AI Model Selection (V2.0)")
        model_selection_frame = ttk.Frame(self.v2_model_frame); model_selection_frame.pack(fill='x', padx=15, pady=15)
        ttk.Label(model_selection_frame, text="选择AI模型:", style='Heading.TLabel').pack(side='left')
        self.selected_model_var = tk.StringVar()
        self.model_combobox = ttk.Combobox(model_selection_frame, textvariable=self.selected_model_var, state='readonly', width=40)
        self.model_combobox.pack(side='left', padx=(10, 5), fill='x', expand=True)
        self.model_combobox.bind('<<ComboboxSelected>>', self.on_model_selected)
        ttk.Button(model_selection_frame, text="[LOAD] 加载模型", style='Accent.TButton', command=self.load_external_model_v2).pack(side='right', padx=(5, 0))
        ttk.Button(model_selection_frame, text="[REFRESH] 刷新", style='Modern.TButton', command=self.refresh_model_list).pack(side='right', padx=(5, 0))
        self.model_info_frame = ttk.Frame(self.v2_model_frame); self.model_info_frame.pack(fill='x', padx=15, pady=(0, 15))
        extractor_frame = ttk.Frame(self.v2_model_frame); extractor_frame.pack(fill='x', padx=15, pady=(0, 15))
        ttk.Label(extractor_frame, text="特征提取器:", style='Heading.TLabel').pack(side='left')
        self.selected_extractor_var = tk.StringVar()
        self.extractor_combobox = ttk.Combobox(extractor_frame, textvariable=self.selected_extractor_var, state='readonly', width=30)
        self.extractor_combobox.pack(side='left', padx=(10, 5))
        self.extractor_combobox.bind('<<ComboboxSelected>>', self.on_extractor_selected)

        self.v1_model_frame = ttk.LabelFrame(main_container, text="[AI] AI Model Configuration (V1.0)")
        model_path_container = ttk.Frame(self.v1_model_frame); model_path_container.pack(fill='x', padx=15, pady=15)
        ttk.Label(model_path_container, text="[AI] AI Model Path:", style='Heading.TLabel').pack(anchor='w', pady=(0, 5))
        model_path_frame = ttk.Frame(model_path_container); model_path_frame.pack(fill='x', pady=5)
        self.model_path_entry = ttk.Entry(model_path_frame, textvariable=self.config_vars['ai_model_path'], width=50, font=('Segoe UI', 9))
        self.model_path_entry.pack(side='left', fill='x', expand=True)
        ttk.Button(model_path_frame, text="📂 Browse", style='Modern.TButton', command=self.select_ai_model_for_analysis).pack(side='right', padx=(5, 0))
        ttk.Button(model_path_frame, text="[LOAD] Load Model", style='Accent.TButton', command=self.load_ai_model_for_analysis).pack(side='right', padx=(5, 0))
        training_frame = ttk.Frame(model_path_container); training_frame.pack(fill='x', pady=(10, 0))
        ttk.Button(training_frame, text="[CONFIG] Train AI Model", style='Modern.TButton', command=self.train_ai_model).pack(side='left', padx=(0, 5))
        ttk.Button(training_frame, text="💾 Save Model", style='Modern.TButton', command=self.save_ai_model).pack(side='left', padx=(0, 5))
        status_container = ttk.Frame(model_path_container); status_container.pack(fill='x', pady=(5, 0))
        ttk.Label(status_container, text="Model Status:", style='Heading.TLabel').pack(side='left')
        self.analysis_model_status_var = tk.StringVar(value="No model loaded")
        ttk.Label(status_container, textvariable=self.analysis_model_status_var, font=('Segoe UI', 9), foreground='gray').pack(side='left', padx=(10, 0))

        control_frame = ttk.LabelFrame(main_container, text="[CONTROLLER] Analysis Control")
        control_frame.pack(fill='x', pady=(0, 20), padx=10)
        button_frame = ttk.Frame(control_frame); button_frame.pack(fill='x', padx=15, pady=15)
        ttk.Button(button_frame, text="[DATA] GZ Traditional Analysis", style='Modern.TButton', command=self.run_gz_traditional_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="[ANALYSIS] Enhanced Analysis", style='Accent.TButton', command=self.run_enhanced_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="[AI] AI Analysis", style='Accent.TButton', command=self.run_ai_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="[START] Quick Analysis", style='Success.TButton', command=self.quick_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="💾 Save Results", style='Modern.TButton', command=self.save_results).pack(side='right')
        
        self.switch_ai_system() 
        if self.use_v2_analyzer:
            self.refresh_model_list()
            self.refresh_extractor_list()
            self.update_model_info_display()

    def setup_traditional_results_tab(self):
        traditional_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(traditional_frame, text="GZ Traditional Analysis")
        main_container = ttk.Frame(traditional_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="GZ Traditional Analysis Results", style='Title.TLabel').pack(pady=(0, 20))
        results_frame = ttk.LabelFrame(main_container, text="Analysis Results")
        results_frame.pack(fill='both', expand=True, padx=10)
        self.traditional_text = tk.Text(results_frame, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        traditional_scroll = ttk.Scrollbar(results_frame, orient='vertical', command=self.traditional_text.yview)
        self.traditional_text.configure(yscrollcommand=traditional_scroll.set)
        self.traditional_text.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        traditional_scroll.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.traditional_text)
        self.traditional_text.insert(tk.END, "GZ Traditional Analysis Results\n" + "=" * 50 + "\n\nNo analysis results yet.\nPlease load data and run GZ traditional analysis to see results here.\n")

    def setup_enhanced_results_tab(self):
        enhanced_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(enhanced_frame, text="Enhanced Analysis")
        main_container = ttk.Frame(enhanced_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="Enhanced Analysis Results", style='Title.TLabel').pack(pady=(0, 20))

        # Control frame for enhanced analysis
        control_frame = ttk.LabelFrame(main_container, text="Enhanced Analysis Control")
        control_frame.pack(fill='x', pady=(0, 15), padx=10)
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill='x', padx=15, pady=15)
        ttk.Button(button_frame, text="Run Enhanced Analysis", style='Accent.TButton', command=self.run_enhanced_analysis).pack(side='left', padx=(0, 10))
        ttk.Button(button_frame, text="💾 Save Enhanced Results", style='Modern.TButton', command=self.save_enhanced_results).pack(side='right')

        results_frame = ttk.LabelFrame(main_container, text="Enhanced Analysis Results")
        results_frame.pack(fill='both', expand=True, padx=10)
        self.enhanced_text = tk.Text(results_frame, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        enhanced_scroll = ttk.Scrollbar(results_frame, orient='vertical', command=self.enhanced_text.yview)
        self.enhanced_text.configure(yscrollcommand=enhanced_scroll.set)
        self.enhanced_text.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        enhanced_scroll.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.enhanced_text)
        self.enhanced_text.insert(tk.END, "Enhanced Analysis Results\n" + "=" * 50 + "\n\nNo analysis results yet.\nPlease load data and run enhanced analysis to see results here.\n")

    def setup_ai_results_tab(self):
        ai_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(ai_frame, text="AI Analysis")
        main_container = ttk.Frame(ai_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="AI Enhanced Analysis Results", style='Title.TLabel').pack(pady=(0, 20))
        results_frame = ttk.LabelFrame(main_container, text="AI Analysis Results")
        results_frame.pack(fill='both', expand=True, padx=10)
        self.ai_text = tk.Text(results_frame, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        ai_scroll = ttk.Scrollbar(results_frame, orient='vertical', command=self.ai_text.yview)
        self.ai_text.configure(yscrollcommand=ai_scroll.set)
        self.ai_text.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        ai_scroll.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.ai_text)
        self.ai_text.insert(tk.END, "AI Enhanced Analysis Results\n" + "=" * 50 + "\n\nNo analysis results yet.\nPlease load data and run AI analysis to see results here.\n")

    def setup_comparison_tab(self):
        comparison_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(comparison_frame, text="Comparison")
        main_container = ttk.Frame(comparison_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="Comparative Analysis", style='Title.TLabel').pack(pady=(0, 20))
        results_frame = ttk.LabelFrame(main_container, text="Comparison Results")
        results_frame.pack(fill='both', expand=True, padx=10)
        self.comparison_text = tk.Text(results_frame, font=('Consolas', 10), wrap=tk.WORD, height=25, width=100)
        comparison_scroll = ttk.Scrollbar(results_frame, orient='vertical', command=self.comparison_text.yview)
        self.comparison_text.configure(yscrollcommand=comparison_scroll.set)
        self.comparison_text.pack(side='left', fill='both', expand=True, padx=15, pady=15)
        comparison_scroll.pack(side='right', fill='y', pady=15)
        self.bind_mousewheel(self.comparison_text)
        self.comparison_text.insert(tk.END, "Comparative Analysis Results\n" + "=" * 50 + "\n\nNo comparison results yet.\nPlease run both GZ traditional and AI analyses to see comparison here.\n")

    def setup_visualization_tab(self):
        viz_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(viz_frame, text="Visualization")
        main_container = ttk.Frame(viz_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        control_frame = ttk.LabelFrame(main_container, text="Visualization Controls")
        control_frame.pack(fill='x', pady=(0, 15), padx=10)
        # 单行按钮布局 - 将所有按钮排在一行以减少控制面板高度
        button_frame = ttk.Frame(control_frame); button_frame.pack(fill='x', padx=15, pady=10)

        # 左侧按钮组
        left_buttons = ttk.Frame(button_frame); left_buttons.pack(side='left', fill='x', expand=True)
        ttk.Button(left_buttons, text="Plot Analysis Results", style='Accent.TButton', command=self.plot_analysis_results).pack(side='left', padx=(0, 5))
        ttk.Button(left_buttons, text="Speed Data", style='Modern.TButton', command=self.plot_speed_data).pack(side='left', padx=(0, 5))
        ttk.Button(left_buttons, text="Amplitude Data", style='Modern.TButton', command=self.plot_amplitude_data).pack(side='left', padx=(0, 5))
        ttk.Button(left_buttons, text="Energy Data", style='Modern.TButton', command=self.plot_energy_data).pack(side='left', padx=(0, 5))
        ttk.Button(left_buttons, text="PSD Data", style='Modern.TButton', command=self.plot_psd_data).pack(side='left', padx=(0, 5))

        # 中间按钮组 - 云图
        middle_buttons = ttk.Frame(button_frame); middle_buttons.pack(side='left', padx=(10, 0))
        ttk.Button(middle_buttons, text="Speed Contour", style='Modern.TButton', command=self.plot_speed_contour).pack(side='left', padx=(0, 5))
        ttk.Button(middle_buttons, text="Amplitude Contour", style='Modern.TButton', command=self.plot_amplitude_contour).pack(side='left', padx=(0, 5))
        ttk.Button(middle_buttons, text="Energy Contour", style='Modern.TButton', command=self.plot_energy_contour).pack(side='left', padx=(0, 5))
        ttk.Button(middle_buttons, text="PSD Contour", style='Modern.TButton', command=self.plot_psd_contour).pack(side='left', padx=(0, 5))

        # 右侧按钮
        ttk.Button(button_frame, text="Save Plot", style='Modern.TButton', command=self.save_plot).pack(side='right')
        viz_display_frame = ttk.LabelFrame(main_container, text="Plots")
        viz_display_frame.pack(fill='both', expand=True, padx=10)
        self.fig = Figure(figsize=(16, 12), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.fig, viz_display_frame)
        self.canvas.get_tk_widget().pack(fill='both', expand=True, padx=15, pady=15)
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        toolbar_frame = ttk.Frame(viz_display_frame); toolbar_frame.pack(fill='x', padx=15, pady=(0, 15))
        self.toolbar = NavigationToolbar2Tk(self.canvas, toolbar_frame); self.toolbar.update()

    def setup_config_tab(self):
        config_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(config_frame, text="Configuration")
        main_container = ttk.Frame(config_frame, style='Modern.TFrame')
        main_container.pack(fill='both', expand=True, padx=20, pady=15)
        ttk.Label(main_container, text="System Configuration", style='Title.TLabel').pack(pady=(0, 20))

        # Create notebook for different config sections
        config_notebook = ttk.Notebook(main_container, style='Modern.TNotebook')
        config_notebook.pack(fill='both', expand=True)

        # GZ Method Configuration
        gz_frame = ttk.Frame(config_notebook, style='Modern.TFrame')
        config_notebook.add(gz_frame, text="GZ Method")
        gz_config_frame = ttk.LabelFrame(gz_frame, text="GZ Method Thresholds")
        gz_config_frame.pack(fill='both', expand=True, padx=10, pady=10)
        self.create_threshold_config(gz_config_frame)

        # Enhanced Analysis Configuration
        enhanced_frame = ttk.Frame(config_notebook, style='Modern.TFrame')
        config_notebook.add(enhanced_frame, text="Enhanced Analysis")
        self.create_enhanced_config_ui(enhanced_frame)

        # General Settings
        general_frame = ttk.Frame(config_notebook, style='Modern.TFrame')
        config_notebook.add(general_frame, text="🎛️ General")
        general_settings_frame = ttk.LabelFrame(general_frame, text="🎛️ General Settings")
        general_settings_frame.pack(fill='x', pady=10, padx=10)
        ttk.Checkbutton(general_settings_frame, text="Auto-run analysis when data is loaded", variable=self.config_vars['auto_analysis']).pack(anchor='w', padx=15, pady=10)
        ttk.Checkbutton(general_settings_frame, text="Show detailed analysis information", variable=self.config_vars['show_details']).pack(anchor='w', padx=15, pady=(0, 10))
        ttk.Checkbutton(general_settings_frame, text="Enable enhanced analysis in quick analysis", variable=self.config_vars['enhanced_analysis_enabled']).pack(anchor='w', padx=15, pady=(0, 10))

        config_button_frame = ttk.Frame(main_container); config_button_frame.pack(fill='x', pady=20, padx=10)
        ttk.Button(config_button_frame, text="💾 Save Configuration", style='Success.TButton', command=self.save_config).pack(side='left', padx=(0, 10))
        ttk.Button(config_button_frame, text="Load Configuration", style='Modern.TButton', command=self.load_config).pack(side='left', padx=(0, 10))
        ttk.Button(config_button_frame, text="Reset to Defaults", style='Modern.TButton', command=self.reset_config).pack(side='right')

    def create_threshold_config(self, parent):
        canvas = tk.Canvas(parent); scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.pack(side="left", fill="both", expand=True, padx=15, pady=15)
        scrollbar.pack(side="right", fill="y", pady=15)
        grid_frame = ttk.Frame(scrollable_frame); grid_frame.pack(fill='x', padx=10, pady=10)
        row = 0

        # 1. Sp (%) 阈值配置
        ttk.Label(grid_frame, text="Sp (%) 阈值配置:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(0, 10)); row += 1
        ttk.Label(grid_frame, text="sp ≥ 100%:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_ge_100'], width=8).grid(row=row, column=1, padx=5); row += 1
        ttk.Label(grid_frame, text="85% ≤ sp < 100%:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_85_lt_100_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_85_lt_100_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="75% ≤ sp < 85%:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_75_lt_85_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_75_lt_85_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="65% ≤ sp < 75%:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_65_lt_75_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['sp_65_lt_75_max'], width=8).grid(row=row, column=4, padx=2); row += 2

        # 2. Ad (dB) 阈值配置
        ttk.Label(grid_frame, text="Ad (dB) 阈值配置:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10)); row += 1
        ttk.Label(grid_frame, text="ad ≤ 0:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_le_0'], width=8).grid(row=row, column=1, padx=5); row += 1
        ttk.Label(grid_frame, text="0 < ad ≤ 4:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_0_le_4_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_0_le_4_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="4 < ad ≤ 8:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_4_le_8_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_4_le_8_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="8 < ad ≤ 12:").grid(row=row, column=0, sticky='w', padx=(20, 5)); ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_8_le_12_min'], width=8).grid(row=row, column=2, padx=2); ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2)); ttk.Entry(grid_frame, textvariable=self.config_vars['ad_gt_8_le_12_max'], width=8).grid(row=row, column=4, padx=2); row += 2

        # 3. Energy% 阈值配置
        ttk.Label(grid_frame, text="Energy% 阈值配置:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10)); row += 1
        ttk.Label(grid_frame, text="I(j,i)=1 (正常):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_energy_normal_min'], width=8).grid(row=row, column=2, padx=2)
        ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_energy_normal_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="I(j,i)=2 (轻微畸变):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_energy_light_min'], width=8).grid(row=row, column=2, padx=2)
        ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_energy_light_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="I(j,i)=3 (明显畸变):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_energy_obvious_min'], width=8).grid(row=row, column=2, padx=2)
        ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_energy_obvious_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="I(j,i)=4 (严重畸变):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_energy_severe_min'], width=8).grid(row=row, column=2, padx=2)
        ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_energy_severe_max'], width=8).grid(row=row, column=4, padx=2); row += 2

        # 4. PSD 阈值配置
        ttk.Label(grid_frame, text="PSD 阈值配置:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10)); row += 1
        ttk.Label(grid_frame, text="I(j,i)=1 (正常):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_psd_normal_min'], width=8).grid(row=row, column=2, padx=2)
        ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_psd_normal_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="I(j,i)=2 (轻微畸变):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_psd_light_min'], width=8).grid(row=row, column=2, padx=2)
        ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_psd_light_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="I(j,i)=3 (明显畸变):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_psd_obvious_min'], width=8).grid(row=row, column=2, padx=2)
        ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_psd_obvious_max'], width=8).grid(row=row, column=4, padx=2); row += 1
        ttk.Label(grid_frame, text="I(j,i)=4 (严重畸变):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Label(grid_frame, text="最小值:").grid(row=row, column=1, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_psd_severe_min'], width=8).grid(row=row, column=2, padx=2)
        ttk.Label(grid_frame, text="最大值:").grid(row=row, column=3, sticky='w', padx=(10, 2))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_psd_severe_max'], width=8).grid(row=row, column=4, padx=2); row += 2

        # 5. 深度范围配置
        ttk.Label(grid_frame, text="深度范围配置:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10)); row += 1
        ttk.Label(grid_frame, text="K值计算深度范围 (m):").grid(row=row, column=0, sticky='w', padx=(20, 5))
        ttk.Entry(grid_frame, textvariable=self.config_vars['gz_depth_range'], width=8).grid(row=row, column=1, padx=5)
        ttk.Label(grid_frame, text="(默认0.5m，即50cm范围内的K值)", font=('Segoe UI', 9), foreground='gray').grid(row=row, column=2, columnspan=3, sticky='w', padx=(10, 0)); row += 2

        # 6. GZ Traditional Analysis 指标启用配置
        ttk.Label(grid_frame, text="GZ Traditional Analysis 指标启用:", style='Heading.TLabel').grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10)); row += 1
        indicators_frame = ttk.Frame(grid_frame)
        indicators_frame.grid(row=row, column=0, columnspan=4, sticky='w', padx=(20, 0), pady=(5, 10)); row += 1
        ttk.Checkbutton(indicators_frame, text="Speed% (声速)", variable=self.config_vars['gz_enable_speed']).pack(side='left', padx=(0, 20))
        ttk.Checkbutton(indicators_frame, text="Amp% (波幅)", variable=self.config_vars['gz_enable_amplitude']).pack(side='left', padx=(0, 20))
        ttk.Checkbutton(indicators_frame, text="Energy% (能量)", variable=self.config_vars['gz_enable_energy']).pack(side='left', padx=(0, 20))
        ttk.Checkbutton(indicators_frame, text="PSD (功率谱密度)", variable=self.config_vars['gz_enable_psd']).pack(side='left')
        def _on_mousewheel(event): canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        def _bind_to_mousewheel(event): canvas.bind_all("<MouseWheel>", _on_mousewheel)
        def _unbind_from_mousewheel(event): canvas.unbind_all("<MouseWheel>")
        canvas.bind('<Enter>', _bind_to_mousewheel); canvas.bind('<Leave>', _unbind_from_mousewheel)

    def create_enhanced_config_ui(self, parent):
        """创建增强分析配置界面"""
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        scrollable_frame.bind("<Configure>", lambda e: canvas.configure(scrollregion=canvas.bbox("all")))
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        canvas.pack(side="left", fill="both", expand=True, padx=15, pady=15)
        scrollbar.pack(side="right", fill="y", pady=15)

        # Main configuration frame
        main_frame = ttk.Frame(scrollable_frame)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Indicator enable/disable section
        indicator_frame = ttk.LabelFrame(main_frame, text="指标启用设置")
        indicator_frame.pack(fill='x', pady=(0, 15))

        indicator_grid = ttk.Frame(indicator_frame)
        indicator_grid.pack(fill='x', padx=15, pady=15)

        ttk.Checkbutton(indicator_grid, text="启用Speed%指标", variable=self.config_vars['enable_speed']).grid(row=0, column=0, sticky='w', padx=(0, 20))
        ttk.Checkbutton(indicator_grid, text="启用Amp%指标", variable=self.config_vars['enable_amp']).grid(row=0, column=1, sticky='w', padx=(0, 20))
        ttk.Checkbutton(indicator_grid, text="启用Energy%指标", variable=self.config_vars['enable_energy']).grid(row=1, column=0, sticky='w', padx=(0, 20))
        ttk.Checkbutton(indicator_grid, text="启用PSD指标", variable=self.config_vars['enable_psd']).grid(row=1, column=1, sticky='w', padx=(0, 20))

        # Threshold settings section
        threshold_frame = ttk.LabelFrame(main_frame, text="分类阈值设置")
        threshold_frame.pack(fill='x', pady=(0, 15))

        threshold_grid = ttk.Frame(threshold_frame)
        threshold_grid.pack(fill='x', padx=15, pady=15)

        # Speed% thresholds
        row = 0
        ttk.Label(threshold_grid, text="Speed% 阈值:", font=('Segoe UI', 10, 'bold')).grid(row=row, column=0, columnspan=4, sticky='w', pady=(0, 10))
        row += 1

        categories = ['normal', 'light', 'obvious', 'severe']
        category_names = ['正常', '轻微畸变', '明显畸变', '严重畸变']

        for i, (cat, name) in enumerate(zip(categories, category_names)):
            ttk.Label(threshold_grid, text=f"{name}:").grid(row=row, column=0, sticky='w', padx=(20, 5))
            ttk.Entry(threshold_grid, textvariable=self.config_vars[f'speed_{cat}_min'], width=8).grid(row=row, column=1, padx=2)
            ttk.Label(threshold_grid, text="~").grid(row=row, column=2, padx=2)
            ttk.Entry(threshold_grid, textvariable=self.config_vars[f'speed_{cat}_max'], width=8).grid(row=row, column=3, padx=2)
            row += 1

        # Amp% thresholds
        ttk.Label(threshold_grid, text="Amp% 阈值:", font=('Segoe UI', 10, 'bold')).grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10))
        row += 1

        for i, (cat, name) in enumerate(zip(categories, category_names)):
            ttk.Label(threshold_grid, text=f"{name}:").grid(row=row, column=0, sticky='w', padx=(20, 5))
            ttk.Entry(threshold_grid, textvariable=self.config_vars[f'amp_{cat}_min'], width=8).grid(row=row, column=1, padx=2)
            ttk.Label(threshold_grid, text="~").grid(row=row, column=2, padx=2)
            ttk.Entry(threshold_grid, textvariable=self.config_vars[f'amp_{cat}_max'], width=8).grid(row=row, column=3, padx=2)
            row += 1

        # Energy% thresholds
        ttk.Label(threshold_grid, text="Energy% 阈值:", font=('Segoe UI', 10, 'bold')).grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10))
        row += 1

        for i, (cat, name) in enumerate(zip(categories, category_names)):
            ttk.Label(threshold_grid, text=f"{name}:").grid(row=row, column=0, sticky='w', padx=(20, 5))
            ttk.Entry(threshold_grid, textvariable=self.config_vars[f'energy_{cat}_min'], width=8).grid(row=row, column=1, padx=2)
            ttk.Label(threshold_grid, text="~").grid(row=row, column=2, padx=2)
            ttk.Entry(threshold_grid, textvariable=self.config_vars[f'energy_{cat}_max'], width=8).grid(row=row, column=3, padx=2)
            row += 1

        # PSD thresholds
        ttk.Label(threshold_grid, text="PSD 阈值:", font=('Segoe UI', 10, 'bold')).grid(row=row, column=0, columnspan=4, sticky='w', pady=(10, 10))
        row += 1

        for i, (cat, name) in enumerate(zip(categories, category_names)):
            ttk.Label(threshold_grid, text=f"{name}:").grid(row=row, column=0, sticky='w', padx=(20, 5))
            ttk.Entry(threshold_grid, textvariable=self.config_vars[f'psd_{cat}_min'], width=8).grid(row=row, column=1, padx=2)
            ttk.Label(threshold_grid, text="~").grid(row=row, column=2, padx=2)
            ttk.Entry(threshold_grid, textvariable=self.config_vars[f'psd_{cat}_max'], width=8).grid(row=row, column=3, padx=2)
            row += 1

        # Analysis parameters section
        params_frame = ttk.LabelFrame(main_frame, text="📏 分析参数")
        params_frame.pack(fill='x', pady=(0, 15))

        params_grid = ttk.Frame(params_frame)
        params_grid.pack(fill='x', padx=15, pady=15)

        ttk.Label(params_grid, text="连续长度阈值 (m):").grid(row=0, column=0, sticky='w', padx=(0, 10))
        ttk.Entry(params_grid, textvariable=self.config_vars['continuous_threshold'], width=10).grid(row=0, column=1, padx=5)

        # Mouse wheel scrolling
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        def _bind_to_mousewheel(event):
            canvas.bind_all("<MouseWheel>", _on_mousewheel)
        def _unbind_from_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")
        canvas.bind('<Enter>', _bind_to_mousewheel)
        canvas.bind('<Leave>', _unbind_from_mousewheel)

    def select_file(self):
        file_path = filedialog.askopenfilename(title="Select Pile Data File", filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")])
        if file_path:
            self.current_file = file_path
            self.file_path_var.set(file_path)
            self.load_data_file(file_path)

    def reload_file(self):
        if self.current_file: self.load_data_file(self.current_file)
        else: messagebox.showwarning("Warning", "No file selected to reload")

    def load_data_file(self, file_path):
        print(f"[FILE] Loading data file: {file_path}")
        try:
            self.data_df = self.parse_data_file(file_path)
            if self.data_df is None or self.data_df.empty:
                print("[ERROR] Data loading failed or file is empty")
                messagebox.showerror("Error", "Failed to load data or file is empty"); return
            print(f"[SUCCESS] Data loaded successfully: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            self.update_data_preview()
            if self.config_vars['auto_analysis'].get():
                print("[START] Auto-running analysis..."); self.quick_analysis()
            print("[COMPLETE] Data loading completed successfully")
        except Exception as e:
            print(f"[ERROR] Data loading error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"Failed to load file: {str(e)}")

    def parse_data_file(self, file_path):
        try:
            df = pd.read_csv(file_path, sep='\t', header=0)
            print(f"原始数据列名: {list(df.columns)}, 数据形状: {df.shape}")
            column_mapping = {}
            for col_name in df.columns:
                col_lower = col_name.lower().replace('_', '').replace(' ', '').replace('%', '')
                if 'depth' in col_lower: column_mapping[col_name] = 'Depth'
                elif '1-2speed' in col_lower or '12speed' in col_lower : column_mapping[col_name] = 'S1'
                elif '1-2amp' in col_lower or '12amp' in col_lower: column_mapping[col_name] = 'A1'
                elif '1-3speed' in col_lower or '13speed' in col_lower: column_mapping[col_name] = 'S2'
                elif '1-3amp' in col_lower or '13amp' in col_lower: column_mapping[col_name] = 'A2'
                elif '2-3speed' in col_lower or '23speed' in col_lower: column_mapping[col_name] = 'S3'
                elif '2-3amp' in col_lower or '23amp' in col_lower: column_mapping[col_name] = 'A3'
                # Add energy% columns
                elif '1-2energy' in col_lower or '12energy' in col_lower: column_mapping[col_name] = 'E1'
                elif '1-3energy' in col_lower or '13energy' in col_lower: column_mapping[col_name] = 'E2'
                elif '2-3energy' in col_lower or '23energy' in col_lower: column_mapping[col_name] = 'E3'
                # Add PSD columns
                elif '1-2psd' in col_lower or '12psd' in col_lower: column_mapping[col_name] = 'P1'
                elif '1-3psd' in col_lower or '13psd' in col_lower: column_mapping[col_name] = 'P2'
                elif '2-3psd' in col_lower or '23psd' in col_lower: column_mapping[col_name] = 'P3'

            df.rename(columns=column_mapping, inplace=True)
            print(f"重命名后列名: {list(df.columns)}")
            required_columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3', 'E1', 'E2', 'E3', 'P1', 'P2', 'P3']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"警告：缺少列 {missing_columns}")
                if len(missing_columns) > 6:
                    messagebox.showwarning("Data Warning", f"文件可能格式不正确，缺少关键列: {missing_columns}")
            for col in required_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                else:
                    df[col] = np.nan
                    print(f"Added missing column '{col}' with NaNs.")

            original_len = len(df); df.dropna(subset=['Depth'], inplace=True)
            print(f"处理缺失值后: {len(df)} 行 (原始: {original_len} 行)")
            return df
        except Exception as e:
            print(f"数据解析错误: {str(e)}"); traceback.print_exc(); return None

    def update_data_preview(self):
        if self.data_df is None or self.data_df.empty:
            self.data_info_var.set("No data loaded"); return
        for item in self.data_tree.get_children(): self.data_tree.delete(item)
        display_df = self.data_df.head(100)
        cols_to_display = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3', 'E1', 'E2', 'E3', 'P1', 'P2', 'P3']
        for index, row in display_df.iterrows():
            values = []
            for col in cols_to_display:
                if col in row.index and pd.notnull(row[col]):
                    values.append(f"{row[col]:.2f}")
                else:
                    values.append("N/A")
            self.data_tree.insert('', 'end', values=values)
        info_text = f"Loaded {len(self.data_df)} rows, {len(self.data_df.columns)} columns"
        if len(self.data_df) > 100: info_text += " (showing first 100 rows)"
        self.data_info_var.set(info_text)

    def quick_analysis(self):
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return
        self.run_gz_traditional_analysis()

        # Run enhanced analysis if enabled
        if self.config_vars['enhanced_analysis_enabled'].get():
            self.run_enhanced_analysis()

        ai_system_version = self.ai_system_var.get()
        if ai_system_version == "v2" and self.use_v2_analyzer and self.ai_analyzer_v2 and self.ai_analyzer_v2.model_manager.current_model:
            self.run_ai_analysis()
        elif ai_system_version == "v1":
             model_path = self.config_vars['ai_model_path'].get()
             if model_path and os.path.exists(model_path) or hasattr(self.ai_analyzer.classifier_model, 'classes_'):
                self.run_ai_analysis()
        if 'gz_traditional' in self.analysis_results and 'ai' in self.analysis_results:
            self.generate_comparison()

    def run_gz_traditional_analysis(self):
        print("[ANALYSIS] Starting GZ traditional analysis...")
        if self.data_df is None or self.data_df.empty:
            print("[ERROR] No data loaded"); messagebox.showwarning("Warning", "Please load data first"); return
        try:
            print(f"[DATA] Data shape: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            self.main_status_var.set("Running GZ traditional analysis..."); self.root.update()
            result = self.perform_gz_analysis()
            print(f"[SUCCESS] GZ traditional analysis result: {result.get('final_category', 'N/A')}")
            self.analysis_results['gz_traditional'] = result
            self.display_gz_traditional_result(result)
            self.main_status_var.set("GZ traditional analysis completed")
            print("[COMPLETE] GZ traditional analysis completed successfully")
        except Exception as e:
            print(f"[ERROR] GZ traditional analysis error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"GZ traditional analysis failed: {str(e)}")
            self.main_status_var.set("GZ traditional analysis failed")

    def perform_gz_analysis(self):
        if self.data_df is None or self.data_df.empty: return None
        default_bi_ratio = self.config_vars['bi_ratio_default'].get()
        gz_config = self.create_dynamic_gz_config()

        # 获取指标启用状态
        enabled_indicators = {
            'speed': self.config_vars['gz_enable_speed'].get(),
            'amplitude': self.config_vars['gz_enable_amplitude'].get(),
            'energy': self.config_vars['gz_enable_energy'].get(),
            'psd': self.config_vars['gz_enable_psd'].get()
        }

        results = {'I_ji_values': {}, 'K_values': {}, 'final_category': None, 'report_details': [],
                  'analysis_summary': "", 'detailed_analysis': {}, 'config_used': gz_config,
                  'enabled_indicators': enabled_indicators}

        # 基础必需列
        required_gz_cols = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
        # 可选的Energy和PSD列
        optional_cols = ['E1', 'E2', 'E3', 'P1', 'P2', 'P3']

        temp_df = self.data_df.copy()
        for col in required_gz_cols + optional_cols:
            if col not in temp_df.columns:
                temp_df[col] = np.nan

        for index, row in temp_df.iterrows():
            depth = row['Depth']
            if pd.isna(depth): continue

            profiles_data = {
                '1-2': {
                    'speed': row.get('S1', np.nan),
                    'amplitude': row.get('A1', np.nan),
                    'energy': row.get('E1', np.nan),
                    'psd': row.get('P1', np.nan)
                },
                '1-3': {
                    'speed': row.get('S2', np.nan),
                    'amplitude': row.get('A2', np.nan),
                    'energy': row.get('E2', np.nan),
                    'psd': row.get('P2', np.nan)
                },
                '2-3': {
                    'speed': row.get('S3', np.nan),
                    'amplitude': row.get('A3', np.nan),
                    'energy': row.get('E3', np.nan),
                    'psd': row.get('P3', np.nan)
                }
            }

            K_values_at_depth = {}
            for profile, data in profiles_data.items():
                # 检查必需的数据是否可用
                has_basic_data = (enabled_indicators['speed'] and pd.notnull(data['speed'])) or \
                               (enabled_indicators['amplitude'] and pd.notnull(data['amplitude']))

                if has_basic_data:
                    # 使用增强的I(j,i)计算函数
                    I_ji_value = calculate_I_ji_enhanced(
                        Sp=data['speed'] if enabled_indicators['speed'] and pd.notnull(data['speed']) else None,
                        Ad=data['amplitude'] if enabled_indicators['amplitude'] and pd.notnull(data['amplitude']) else None,
                        Energy=data['energy'] if enabled_indicators['energy'] and pd.notnull(data['energy']) else None,
                        PSD=data['psd'] if enabled_indicators['psd'] and pd.notnull(data['psd']) else None,
                        config=gz_config,
                        enabled_indicators=enabled_indicators
                    )
                    K_values_at_depth[profile] = I_ji_value

            if K_values_at_depth:
                # 取最大I(j,i)值作为该深度的I(j,i)值
                I_ji_max = max(K_values_at_depth.values())
                results['I_ji_values'][depth] = K_values_at_depth  # 存储各剖面的I(j,i)值

                # 计算K值：在指定深度范围内的I(j,i)值
                depth_range = self.config_vars['gz_depth_range'].get()  # 获取深度范围设置
                depths_in_range = []

                # 收集深度范围内的所有I(j,i)值
                for d, i_ji_dict in results['I_ji_values'].items():
                    if abs(d - depth) <= depth_range:
                        depths_in_range.extend(i_ji_dict.values())

                # K值为深度范围内最大的I(j,i)值
                if depths_in_range:
                    K_i = max(depths_in_range)
                else:
                    K_i = I_ji_max

                results['K_values'][depth] = K_i
        final_category, report_details = determine_final_category(results['K_values'])
        results['final_category'] = final_category; results['report_details'] = report_details
        results['analysis_summary'] = self.generate_gz_analysis_summary(results)
        return results

    def perform_enhanced_analysis(self):
        """执行增强分析系统"""
        if self.data_df is None or self.data_df.empty:
            return None

        print("[ANALYSIS] Starting enhanced analysis...")

        # 获取配置
        enhanced_config = self.create_enhanced_config()
        enabled_indicators = {
            'speed': self.config_vars['enable_speed'].get(),
            'amp': self.config_vars['enable_amp'].get(),
            'energy': self.config_vars['enable_energy'].get(),
            'psd': self.config_vars['enable_psd'].get()
        }
        continuous_threshold = self.config_vars['continuous_threshold'].get()

        # 准备数据
        required_cols = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3', 'E1', 'E2', 'E3', 'P1', 'P2', 'P3']
        temp_df = self.data_df.copy()
        for col in required_cols:
            if col not in temp_df.columns:
                temp_df[col] = np.nan

        # 分析结果存储
        depth_classifications = {}  # {depth: classification}
        depth_classifications_by_profile = {}  # {depth: {profile: classification}}

        # 对每个深度的每个剖面进行分类
        for index, row in temp_df.iterrows():
            depth = row['Depth']
            if pd.isna(depth):
                continue

            profile_classifications = {}

            # 分析三个剖面
            profiles = [
                ('1-2', row.get('S1', np.nan), row.get('A1', np.nan), row.get('E1', np.nan), row.get('P1', np.nan)),
                ('1-3', row.get('S2', np.nan), row.get('A2', np.nan), row.get('E2', np.nan), row.get('P2', np.nan)),
                ('2-3', row.get('S3', np.nan), row.get('A3', np.nan), row.get('E3', np.nan), row.get('P3', np.nan))
            ]

            for profile_name, speed, amp, energy, psd in profiles:
                classification = classify_point_by_indicators(
                    speed, amp, energy, psd, enhanced_config, enabled_indicators
                )
                profile_classifications[profile_name] = classification

            # 取该深度最严重的分类作为深度分类
            severity_order = ['正常', '轻微畸变', '明显畸变', '严重畸变']
            classifications = list(profile_classifications.values())
            if classifications:
                max_severity_index = max(severity_order.index(c) for c in classifications)
                depth_classification = severity_order[max_severity_index]
            else:
                depth_classification = '正常'

            depth_classifications[depth] = depth_classification
            depth_classifications_by_profile[depth] = profile_classifications

        # 连续性分析
        continuity_analysis = analyze_continuity(depth_classifications, continuous_threshold)

        # 横向缺陷比例计算
        lateral_ratios = calculate_lateral_defect_ratio(depth_classifications_by_profile)

        # 桩类判定
        final_category, report_details = determine_pile_category_enhanced(
            depth_classifications, continuity_analysis, lateral_ratios,
            continuous_threshold
        )

        # 生成分析摘要
        analysis_summary = self.generate_enhanced_analysis_summary(
            depth_classifications, continuity_analysis, lateral_ratios,
            final_category, report_details, enhanced_config, enabled_indicators
        )

        results = {
            'final_category': final_category,
            'report_details': report_details,
            'analysis_summary': analysis_summary,
            'depth_classifications': depth_classifications,
            'depth_classifications_by_profile': depth_classifications_by_profile,
            'continuity_analysis': continuity_analysis,
            'lateral_ratios': lateral_ratios,
            'config_used': enhanced_config,
            'enabled_indicators': enabled_indicators,
            'continuous_threshold': continuous_threshold
        }

        print(f"[SUCCESS] Enhanced analysis completed: {final_category}")
        return results

    def create_dynamic_gz_config(self):
        sp_ge_100 = self.config_vars['sp_ge_100'].get(); sp_85_min = self.config_vars['sp_85_lt_100_min'].get(); sp_85_max = self.config_vars['sp_85_lt_100_max'].get(); sp_75_min = self.config_vars['sp_75_lt_85_min'].get(); sp_75_max = self.config_vars['sp_75_lt_85_max'].get(); sp_65_min = self.config_vars['sp_65_lt_75_min'].get(); sp_65_max = self.config_vars['sp_65_lt_75_max'].get()
        ad_le_0 = self.config_vars['ad_le_0'].get(); ad_0_min = self.config_vars['ad_gt_0_le_4_min'].get(); ad_0_max = self.config_vars['ad_gt_0_le_4_max'].get(); ad_4_min = self.config_vars['ad_gt_4_le_8_min'].get(); ad_4_max = self.config_vars['ad_gt_4_le_8_max'].get(); ad_8_min = self.config_vars['ad_gt_8_le_12_min'].get(); ad_8_max = self.config_vars['ad_gt_8_le_12_max'].get()
        return {
            'Sp_conditions': {'ge_100': lambda sp: sp >= sp_ge_100, '85_lt_100': lambda sp: sp_85_min <= sp < sp_85_max, '75_lt_85': lambda sp: sp_75_min <= sp < sp_75_max, '65_lt_75': lambda sp: sp_65_min <= sp < sp_65_max, 'lt_65': lambda sp: sp < sp_65_min, 'ge_85': lambda sp: sp >= sp_85_min, 'ge_75': lambda sp: sp >= sp_75_min, 'ge_65': lambda sp: sp >= sp_65_min},
            'Ad_conditions': {'le_0': lambda ad: ad <= ad_le_0, 'gt_0_le_4': lambda ad: ad_0_min < ad <= ad_0_max, 'gt_4_le_8': lambda ad: ad_4_min < ad <= ad_4_max, 'gt_8_le_12': lambda ad: ad_8_min < ad <= ad_8_max, 'gt_12': lambda ad: ad > ad_8_max, 'le_4': lambda ad: ad <= ad_0_max, 'le_8': lambda ad: ad <= ad_4_max, 'le_12': lambda ad: ad <= ad_8_max},
            'Bi_ratio_conditions': {'gt_08': lambda br: br > 0.8, 'gt_05_le_08': lambda br: 0.5 < br <= 0.8, 'gt_05': lambda br: br > 0.5, 'gt_025_le_05': lambda br: 0.25 < br <= 0.5, 'gt_025': lambda br: br > 0.25, 'le_025': lambda br: br <= 0.25}
        }

    def create_enhanced_config(self):
        """创建增强分析配置"""
        return {
            'speed_thresholds': {
                '正常': (self.config_vars['speed_normal_min'].get(), self.config_vars['speed_normal_max'].get()),
                '轻微畸变': (self.config_vars['speed_light_min'].get(), self.config_vars['speed_light_max'].get()),
                '明显畸变': (self.config_vars['speed_obvious_min'].get(), self.config_vars['speed_obvious_max'].get()),
                '严重畸变': (self.config_vars['speed_severe_min'].get(), self.config_vars['speed_severe_max'].get())
            },
            'amp_thresholds': {
                '正常': (self.config_vars['amp_normal_min'].get(), self.config_vars['amp_normal_max'].get()),
                '轻微畸变': (self.config_vars['amp_light_min'].get(), self.config_vars['amp_light_max'].get()),
                '明显畸变': (self.config_vars['amp_obvious_min'].get(), self.config_vars['amp_obvious_max'].get()),
                '严重畸变': (self.config_vars['amp_severe_min'].get(), self.config_vars['amp_severe_max'].get())
            },
            'energy_thresholds': {
                '正常': (self.config_vars['energy_normal_min'].get(), self.config_vars['energy_normal_max'].get()),
                '轻微畸变': (self.config_vars['energy_light_min'].get(), self.config_vars['energy_light_max'].get()),
                '明显畸变': (self.config_vars['energy_obvious_min'].get(), self.config_vars['energy_obvious_max'].get()),
                '严重畸变': (self.config_vars['energy_severe_min'].get(), self.config_vars['energy_severe_max'].get())
            },
            'psd_thresholds': {
                '正常': (self.config_vars['psd_normal_min'].get(), self.config_vars['psd_normal_max'].get()),
                '轻微畸变': (self.config_vars['psd_light_min'].get(), self.config_vars['psd_light_max'].get()),
                '明显畸变': (self.config_vars['psd_obvious_min'].get(), self.config_vars['psd_obvious_max'].get()),
                '严重畸变': (self.config_vars['psd_severe_min'].get(), self.config_vars['psd_severe_max'].get())
            },
            'continuous_threshold': self.config_vars['continuous_threshold'].get(),
            'enabled_indicators': {
                'speed': self.config_vars['enable_speed'].get(),
                'amp': self.config_vars['enable_amp'].get(),
                'energy': self.config_vars['enable_energy'].get(),
                'psd': self.config_vars['enable_psd'].get()
            }
        }

    def generate_enhanced_analysis_summary(self, depth_classifications, continuity_analysis,
                                         lateral_ratios, final_category, report_details,
                                         enhanced_config, enabled_indicators):
        """生成增强分析摘要"""
        summary = f"增强桩基完整性分析结果\n" + "=" * 40 + "\n\n"
        summary += f"最终判定: {final_category}\n\n"

        # 启用的指标
        enabled_list = [k for k, v in enabled_indicators.items() if v]
        summary += f"启用指标: {', '.join(enabled_list)}\n\n"

        # 判定依据
        summary += "判定依据:\n"
        for detail in report_details:
            summary += f"- {detail}\n"

        # 分类统计
        summary += f"\n深度分类统计:\n"
        if depth_classifications:
            classification_counts = {}
            for classification in depth_classifications.values():
                classification_counts[classification] = classification_counts.get(classification, 0) + 1

            total_depths = len(depth_classifications)
            for classification in ['正常', '轻微畸变', '明显畸变', '严重畸变']:
                count = classification_counts.get(classification, 0)
                percentage = (count / total_depths) * 100 if total_depths > 0 else 0
                summary += f"{classification}: {count}个深度 ({percentage:.1f}%)\n"

        # 连续性分析摘要
        summary += f"\n连续性分析:\n"
        continuous_anomalies = [depth for depth, analysis in continuity_analysis.items()
                              if analysis['is_continuous'] and analysis['classification'] != '正常']
        if continuous_anomalies:
            summary += f"发现连续异常: {len(continuous_anomalies)}处\n"
            for depth in continuous_anomalies[:5]:  # 只显示前5个
                analysis = continuity_analysis[depth]
                summary += f"  深度{depth:.2f}m: {analysis['classification']}, 连续长度{analysis['continuous_length']:.2f}m\n"
        else:
            summary += "未发现连续异常\n"

        # 横向缺陷比例摘要
        summary += f"\n横向缺陷比例分析:\n"
        high_ratio_depths = []
        for depth, ratios in lateral_ratios.items():
            for classification, ratio in ratios.items():
                if classification != '正常' and ratio > 0.5:
                    high_ratio_depths.append((depth, classification, ratio))

        if high_ratio_depths:
            summary += f"发现高横向比例异常: {len(high_ratio_depths)}处\n"
            for depth, classification, ratio in high_ratio_depths[:5]:
                summary += f"  深度{depth:.2f}m: {classification} ({ratio:.1%})\n"
        else:
            summary += "未发现高横向比例异常\n"

        summary += f"\n总计分析深度: {len(depth_classifications)}个\n"
        summary += f"连续长度阈值: {enhanced_config['continuous_threshold']:.2f}m\n"

        return summary

    def generate_gz_analysis_summary(self, results):
        summary = f"GZ方法桩基完整性分析结果\n" + "=" * 40 + "\n\n" + f"最终判定: {results['final_category']}\n\n判定依据:\n"
        for detail in results['report_details']: summary += f"- {detail}\n"
        summary += f"\nK值分布统计:\n"
        if results['K_values']:
            k_counts = {}
            for k_val in results['K_values'].values(): k_counts[k_val] = k_counts.get(k_val, 0) + 1
            for k_val in sorted(k_counts.keys()):
                count = k_counts[k_val]; percentage = (count / len(results['K_values'])) * 100
                summary += f"K={k_val}: {count}个截面 ({percentage:.1f}%)\n"
        summary += f"\n总计分析截面: {len(results['K_values'])}个\n"
        return summary

    def display_gz_traditional_result(self, result):
        print(f"[DISPLAY] Displaying GZ traditional analysis result. Result keys: {list(result.keys()) if result else 'None'}")
        if result is None: self.traditional_text.delete(1.0, tk.END); self.traditional_text.insert(tk.END, "没有GZ传统分析结果可显示。\n"); return
        self.traditional_text.delete(1.0, tk.END)
        self.traditional_text.insert(tk.END, f"桩基完整性类别: {result.get('final_category', 'N/A')}\n\n")
        summary = result.get('analysis_summary', ''); 
        if summary: self.traditional_text.insert(tk.END, summary + "\n")
        self.traditional_text.insert(tk.END, "详细分析结果:\n" + "-" * 50 + "\n")
        K_values = result.get('K_values', {}); I_ji_values = result.get('I_ji_values', {})
        for depth in sorted(K_values.keys()):
            self.traditional_text.insert(tk.END, f"深度 {depth:.2f}m: K(i) = {K_values[depth]}\n")
            if depth in I_ji_values:
                for profile, I_ji in I_ji_values[depth].items(): self.traditional_text.insert(tk.END, f"  剖面{profile}: I(j,i) = {I_ji}\n")
            self.traditional_text.insert(tk.END, "\n")
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i, "text") == "[DATA] GZ Traditional Analysis": self.notebook.select(i); print("[CLIPBOARD] Auto-switched to GZ Traditional Analysis tab"); break
        self.traditional_text.see(1.0); print("[SUCCESS] GZ traditional result displayed successfully")

    def run_enhanced_analysis(self):
        """运行增强分析"""
        print("[ANALYSIS] Starting enhanced analysis...")
        if self.data_df is None or self.data_df.empty:
            print("[ERROR] No data loaded"); messagebox.showwarning("Warning", "Please load data first"); return
        try:
            print(f"[DATA] Data shape for enhanced analysis: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
            self.main_status_var.set("Running enhanced analysis..."); self.root.update()
            result = self.perform_enhanced_analysis()
            print(f"[SUCCESS] Enhanced analysis result: {result.get('final_category', 'N/A')}")
            self.analysis_results['enhanced'] = result
            self.display_enhanced_result(result)
            self.main_status_var.set("Enhanced analysis completed")
            print("[COMPLETE] Enhanced analysis completed successfully")
        except Exception as e:
            print(f"[ERROR] Enhanced analysis error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"Enhanced analysis failed: {str(e)}")
            self.main_status_var.set("Enhanced analysis failed")

    def display_enhanced_result(self, result):
        """显示增强分析结果"""
        print(f"[DISPLAY] Displaying enhanced analysis result. Result keys: {list(result.keys()) if result else 'None'}")
        if result is None:
            self.enhanced_text.delete(1.0, tk.END)
            self.enhanced_text.insert(tk.END, "没有增强分析结果可显示。\n")
            return

        self.enhanced_text.delete(1.0, tk.END)
        self.enhanced_text.insert(tk.END, f"桩基完整性类别: {result.get('final_category', 'N/A')}\n\n")

        summary = result.get('analysis_summary', '')
        if summary:
            self.enhanced_text.insert(tk.END, summary + "\n")

        self.enhanced_text.insert(tk.END, "详细分析结果:\n" + "-" * 50 + "\n")

        # 显示深度分类
        depth_classifications = result.get('depth_classifications', {})
        depth_classifications_by_profile = result.get('depth_classifications_by_profile', {})
        continuity_analysis = result.get('continuity_analysis', {})
        lateral_ratios = result.get('lateral_ratios', {})

        for depth in sorted(depth_classifications.keys()):
            self.enhanced_text.insert(tk.END, f"深度 {depth:.2f}m: {depth_classifications[depth]}\n")

            # 显示各剖面分类
            if depth in depth_classifications_by_profile:
                for profile, classification in depth_classifications_by_profile[depth].items():
                    self.enhanced_text.insert(tk.END, f"  剖面{profile}: {classification}\n")

            # 显示连续性分析
            if depth in continuity_analysis:
                analysis = continuity_analysis[depth]
                if analysis['classification'] != '正常':
                    self.enhanced_text.insert(tk.END, f"  连续长度: {analysis['continuous_length']:.2f}m")
                    if analysis['is_continuous']:
                        self.enhanced_text.insert(tk.END, " (超过阈值)")
                    self.enhanced_text.insert(tk.END, "\n")

            # 显示横向比例
            if depth in lateral_ratios:
                ratios = lateral_ratios[depth]
                for classification, ratio in ratios.items():
                    if classification != '正常' and ratio > 0:
                        self.enhanced_text.insert(tk.END, f"  {classification}横向比例: {ratio:.1%}\n")

            self.enhanced_text.insert(tk.END, "\n")

        # 自动切换到增强分析标签页
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i, "text") == "[ANALYSIS] Enhanced Analysis":
                self.notebook.select(i); print("[CLIPBOARD] Auto-switched to Enhanced Analysis tab"); break
        self.enhanced_text.see(1.0); print("[SUCCESS] Enhanced result displayed successfully")

    def save_enhanced_results(self):
        """保存增强分析结果"""
        if 'enhanced' not in self.analysis_results:
            messagebox.showwarning("Warning", "No enhanced analysis results to save"); return
        try:
            file_path = filedialog.asksaveasfilename(
                title="Save Enhanced Analysis Results",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("JSON files", "*.json"), ("All files", "*.*")]
            )
            if file_path:
                result = self.analysis_results['enhanced']
                if file_path.endswith('.json'):
                    # Convert result to JSON-serializable format
                    json_result = {}
                    for key, value in result.items():
                        if key == 'config_used':
                            # Skip lambda functions in config
                            continue
                        json_result[key] = value

                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(json_result, f, indent=2, ensure_ascii=False, default=str)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write("增强桩基完整性分析结果报告\n" + "=" * 50 + "\n\n")
                        f.write(result.get('analysis_summary', ''))
                        f.write("\n\n详细分析数据:\n" + "-" * 30 + "\n")
                        f.write(f"最终判定: {result.get('final_category', 'N/A')}\n")
                        f.write(f"连续长度阈值: {result.get('continuous_threshold', 0.5):.2f}m\n")

                messagebox.showinfo("Success", f"Enhanced analysis results saved to {file_path}")
                print(f"[SUCCESS] Enhanced results saved to: {file_path}")
        except Exception as e:
            print(f"[ERROR] Save enhanced results error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"Failed to save enhanced results: {str(e)}")

    def select_ai_model_for_analysis(self): 
        file_path = filedialog.askopenfilename(title="Select AI Model File", filetypes=[("Pickle files", "*.pkl"), ("All files", "*.*")], initialdir=self.ai_models_dir)
        if file_path:
            self.config_vars['ai_model_path'].set(file_path)
            self.analysis_model_status_var.set(f"Selected: {os.path.basename(file_path)}")

    def load_ai_model_for_analysis(self): 
        model_path = self.config_vars['ai_model_path'].get()
        if not model_path: messagebox.showwarning("Warning", "Please select an AI model file first"); return
        if not os.path.exists(model_path): messagebox.showerror("Error", f"AI model file not found: {model_path}"); return
        try:
            if self.ai_analyzer.load_models(model_path): 
                 self.analysis_model_status_var.set(f"[SUCCESS] Loaded (V1): {os.path.basename(model_path)}")
                 messagebox.showinfo("Success", "AI model (V1) loaded successfully!")
            else:
                 self.analysis_model_status_var.set("[ERROR] Failed to load model (V1)")
                 messagebox.showerror("Error", "Failed to load AI model (V1). Check console for details.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load AI model (V1): {str(e)}"); traceback.print_exc()
            self.analysis_model_status_var.set("[ERROR] Failed to load model (V1)")

    def train_ai_model(self): 
        try:
            print("[CONFIG] Starting AI model training (V1)...")
            response = messagebox.askyesnocancel("AI Model Training (V1)", "Do you want to train with current data?\n\nYes: Use current loaded data\nNo: Use synthetic training data\nCancel: Cancel training")
            if response is None: return
            self.main_status_var.set("Training AI model (V1)..."); self.root.update()
            features_for_training, labels_for_training = None, None
            feature_names_for_training = None

            if response: 
                if self.data_df is None or self.data_df.empty:
                    messagebox.showwarning("Warning", "No data loaded for V1 training. Using synthetic data instead.")
                else:
                    features_for_training, feature_names_for_training = self.ai_analyzer.extract_features(self.data_df)
                    if features_for_training.size == 0:
                        messagebox.showwarning("Warning", "Failed to extract features for V1 training. Using synthetic data instead.")
                        features_for_training = None 
                    elif 'gz_traditional' in self.analysis_results:
                        label = self.analysis_results['gz_traditional'].get('final_category', 'I类桩') 
                        labels_for_training = np.array([label] * len(features_for_training)) 
                    else:
                        messagebox.showinfo("Info", "No GZ analysis for labels. V1 training will use synthetic data or unsupervised aspects if applicable.")
                        features_for_training = None 
            
            success = self.ai_analyzer.train_models(features_for_training, labels_for_training)
            
            if success and feature_names_for_training and hasattr(self.ai_analyzer.classifier_model, 'feature_importances_'):
                 if len(feature_names_for_training) == len(self.ai_analyzer.classifier_model.feature_importances_):
                      self.ai_analyzer.feature_importance = dict(zip(feature_names_for_training, self.ai_analyzer.classifier_model.feature_importances_))

            if success:
                self.main_status_var.set("AI model training (V1) completed")
                messagebox.showinfo("Success", "AI model (V1) trained successfully!")
                print("[SUCCESS] AI model training (V1) completed")
            else:
                self.main_status_var.set("AI model training (V1) failed")
                messagebox.showerror("Error", "AI model training (V1) failed")
                print("[ERROR] AI model training (V1) failed")
        except Exception as e:
            print(f"[ERROR] AI model training (V1) error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"AI model training (V1) failed: {str(e)}")
            self.main_status_var.set("AI model training (V1) failed")

    def save_ai_model(self): 
        try:
            if self.ai_analyzer.classifier_model is None:
                messagebox.showwarning("Warning", "No trained V1 model to save. Please train a model first."); return
            file_path = filedialog.asksaveasfilename(title="Save AI Model (V1)", defaultextension=".pkl", filetypes=[("Pickle files", "*.pkl"), ("All files", "*.*")], initialdir=self.ai_models_dir)
            if file_path:
                if self.ai_analyzer.save_models(file_path):
                    messagebox.showinfo("Success", f"AI model (V1) saved to {file_path}")
                    print(f"[SUCCESS] AI model (V1) saved to: {file_path}")
                else: messagebox.showerror("Error", "Failed to save AI model (V1)")
        except Exception as e:
            print(f"[ERROR] Save AI model (V1) error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"Failed to save AI model (V1): {str(e)}")

    def run_ai_analysis(self):
        print("[AI] Starting AI analysis...")
        if self.data_df is None or self.data_df.empty:
            print("[ERROR] No data loaded"); messagebox.showwarning("Warning", "Please load data first"); return
        print(f"[DATA] Data shape for AI: {self.data_df.shape}, Columns: {list(self.data_df.columns)}")
        try:
            self.main_status_var.set("Running AI analysis..."); self.root.update()
            ai_system_version = self.ai_system_var.get()
            raw_ai_result = None 
            current_analyzer_name = "Unknown"
            active_analyzer = None

            if ai_system_version == "v2" and self.use_v2_analyzer and self.ai_analyzer_v2:
                print("[START] Using AI System V2.0 for analysis.")
                current_analyzer_name = "AI System V2.0"
                active_analyzer = self.ai_analyzer_v2
                if not active_analyzer.model_manager.current_model:
                     messagebox.showwarning("AI V2 Warning", "No AI V2 model selected/loaded. Please select or load a model.")
                     self.main_status_var.set("AI V2: No model selected"); return
            else: 
                print("[CONFIG] Using AI System V1.0 (BuiltInAIAnalyzer) for analysis.")
                current_analyzer_name = "AI System V1.0"
                active_analyzer = self.ai_analyzer
                model_path_v1 = self.config_vars['ai_model_path'].get()
                if model_path_v1 and os.path.exists(model_path_v1):
                    if active_analyzer.loaded_model_path != model_path_v1 : 
                        print(f"[LOAD] V1: Loading external model: {model_path_v1}")
                        if not active_analyzer.load_models(model_path_v1):
                             messagebox.showerror("V1 Error", f"Failed to load V1 model from {model_path_v1}. Check console.")
                             self.main_status_var.set("AI V1: Model load failed"); return
                elif not hasattr(active_analyzer.classifier_model, 'classes_'): 
                    print("[WARNING] V1: No external model, built-in not trained. Training default.")
                    active_analyzer._initial_training() 
            
            if active_analyzer:
                 raw_ai_result = active_analyzer.predict(self.data_df.copy())
            else:
                 messagebox.showerror("Error", "No AI analyzer available.")
                 self.main_status_var.set("AI: No analyzer"); return

            if raw_ai_result is None:
                print(f"[ERROR] {current_analyzer_name} prediction failed or returned None.")
                messagebox.showerror("Error", f"{current_analyzer_name} prediction failed. Check console for details.")
                self.main_status_var.set(f"{current_analyzer_name} prediction failed"); return

            # === 处理AI分析结果 ===
            processed_ai_result = {}

            if ai_system_version == "v2":
                # V2系统返回的是罗马数字列表
                if isinstance(raw_ai_result, list) and len(raw_ai_result) > 0:
                    # 取第一个预测结果
                    final_roman_prediction = raw_ai_result[0]
                    processed_ai_result['完整性类别'] = final_roman_prediction
                    processed_ai_result['ai_confidence'] = 0.85  # V2系统默认置信度
                    processed_ai_result['anomaly_score'] = 0.0
                    processed_ai_result['is_anomaly'] = False
                    processed_ai_result['feature_importance'] = {}
                    processed_ai_result['class_probabilities'] = {final_roman_prediction: 1.0}
                else:
                    final_roman_prediction = 'N/A'
                    processed_ai_result['完整性类别'] = final_roman_prediction
                    processed_ai_result['ai_confidence'] = 0.0
                    processed_ai_result['anomaly_score'] = 0.0
                    processed_ai_result['is_anomaly'] = False
                    processed_ai_result['feature_importance'] = {}
                    processed_ai_result['class_probabilities'] = {}
            else:
                # V1系统的处理逻辑
                raw_prediction_value = raw_ai_result.get('完整性类别', 'N/A')
                standard_key_for_pred = self.ai_analyzer._get_standard_key_for_map(raw_prediction_value)
                final_roman_prediction = self.ai_output_to_roman_map.get(standard_key_for_pred)

                if final_roman_prediction is None:
                    final_roman_prediction = 'N/A'
                    print(f"[WARNING] AI prediction '{standard_key_for_pred}' (type: {type(standard_key_for_pred)}) from {current_analyzer_name} could not be mapped to Roman. Defaulting to 'N/A'.")

                processed_ai_result['完整性类别'] = final_roman_prediction

                processed_ai_result['ai_confidence'] = raw_ai_result.get('ai_confidence', 0.0)
                processed_ai_result['anomaly_score'] = raw_ai_result.get('anomaly_score', 0.0)
                processed_ai_result['is_anomaly'] = raw_ai_result.get('is_anomaly', False)
                processed_ai_result['feature_importance'] = raw_ai_result.get('feature_importance', {})

                raw_class_probs = raw_ai_result.get('class_probabilities', {})
                processed_class_probs_roman_keys = {}
                for raw_prob_key, prob_val in raw_class_probs.items():
                    standard_key_for_class_prob = self.ai_analyzer._get_standard_key_for_map(raw_prob_key)
                    final_roman_prob_key = self.ai_output_to_roman_map.get(standard_key_for_class_prob)
                    if final_roman_prob_key is None:
                        final_roman_prob_key = str(standard_key_for_class_prob)
                        print(f"[WARNING] AI class probability key '{standard_key_for_class_prob}' could not be mapped to Roman. Using raw key '{final_roman_prob_key}'.")
                    processed_class_probs_roman_keys[final_roman_prob_key] = prob_val
                processed_ai_result['class_probabilities'] = processed_class_probs_roman_keys
            
            # 生成分析推理
            if ai_system_version == "v2":
                # V2系统的推理生成
                category = processed_ai_result['完整性类别']
                confidence = processed_ai_result['ai_confidence']
                category_chinese = self.roman_to_chinese_display_map.get(category, category)
                processed_ai_result['overall_reasoning'] = f"基于AI System V2.0分析，桩基完整性判定为{category_chinese}（{category}），置信度{confidence:.1%}。"
            else:
                # V1系统的推理生成
                processed_ai_result['overall_reasoning'] = self.ai_analyzer._generate_reasoning(
                     processed_ai_result['完整性类别'],
                     processed_ai_result['ai_confidence'],
                     processed_ai_result['anomaly_score'],
                     processed_ai_result['class_probabilities']
                )
            
            result_to_store_and_display = processed_ai_result
            # ====================================================================

            ai_category_roman = result_to_store_and_display.get('完整性类别', 'N/A')
            ai_confidence = result_to_store_and_display.get('ai_confidence', 0.0)
            print(f"[SUCCESS] {current_analyzer_name} analysis result (Processed Roman): {ai_category_roman}, Confidence: {ai_confidence:.2%}")
            
            self.analysis_results['ai'] = result_to_store_and_display
            self.display_ai_result(result_to_store_and_display) 
            self.main_status_var.set(f"{current_analyzer_name} analysis completed")
            print(f"[COMPLETE] {current_analyzer_name} analysis completed successfully")

        except Exception as e:
            print(f"[ERROR] AI analysis error: {str(e)}"); traceback.print_exc()
            messagebox.showerror("Error", f"AI analysis failed: {str(e)}")
            self.main_status_var.set("AI analysis failed")


    def display_ai_result(self, result):
        print(f"[DISPLAY] Displaying AI analysis result. Result keys: {list(result.keys()) if result else 'None'}")
        if result is None: 
            self.ai_text.delete(1.0, tk.END)
            self.ai_text.insert(tk.END, "没有AI分析结果可显示。\n")
            return
        
        self.ai_text.delete(1.0, tk.END)
        
        ai_category_roman = result.get('完整性类别', 'N/A') 
        category_display_chinese = self.roman_to_chinese_display_map.get(ai_category_roman, ai_category_roman)
        
        self.ai_text.insert(tk.END, f"桩基完整性类别: {category_display_chinese}\n") 
        self.ai_text.insert(tk.END, f"AI置信度: {result.get('ai_confidence', 0.0):.2%}\n")
        self.ai_text.insert(tk.END, f"异常分数: {result.get('anomaly_score', 0.0):.2f}\n\n")
        
        self.ai_text.insert(tk.END, f"AI分析结论: {result.get('overall_reasoning', '无分析结论')}\n\n")
        
        self.ai_text.insert(tk.END, "各类别概率:\n")
        class_probabilities_roman_keys = result.get('class_probabilities', {}) 
        sorted_probs = sorted(class_probabilities_roman_keys.items(), key=lambda item: (item[1], item[0]), reverse=True)
        for class_key_roman, prob in sorted_probs:
            display_class_name_chinese = self.roman_to_chinese_display_map.get(class_key_roman, class_key_roman)
            self.ai_text.insert(tk.END, f"  {display_class_name_chinese}: {prob:.2%}\n")
            
        self.ai_text.insert(tk.END, "\n特征重要性排名 (部分):\n") 
        feature_importance = result.get('feature_importance', {})
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        for i, (feature, importance) in enumerate(sorted_features[:10]):
            self.ai_text.insert(tk.END, f"  {i+1}. {feature}: {importance:.4f}\n")
        
        for i_tab in range(self.notebook.index("end")): 
            if self.notebook.tab(i_tab, "text") == "[AI] AI Analysis": 
                self.notebook.select(i_tab)
                print("[CLIPBOARD] Auto-switched to AI Analysis tab")
                break
        self.ai_text.see(1.0)
        print("[SUCCESS] AI result displayed successfully (using Roman numerals internally, Chinese for display)")

    def run_both_analyses(self):
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return
        self.run_gz_traditional_analysis()
        self.run_ai_analysis() 
        if 'gz_traditional' in self.analysis_results and 'ai' in self.analysis_results:
            self.generate_comparison()

    def generate_comparison(self):
        print("[COMPARE] Generating comparison analysis...")
        if 'gz_traditional' not in self.analysis_results or 'ai' not in self.analysis_results:
            print("[ERROR] Both analyses must be completed for comparison"); return
        try:
            gz_result = self.analysis_results['gz_traditional']
            ai_result = self.analysis_results['ai'] 
            comparison_text = self.create_comparison_text(gz_result, ai_result)
            self.display_comparison_result(comparison_text)
            print("[SUCCESS] Comparison analysis completed successfully")
        except Exception as e:
            print(f"[ERROR] Comparison analysis error: {str(e)}"); traceback.print_exc()

    def create_comparison_text(self, gz_result, ai_result):
        comparison = "[COMPARE] GZ传统方法 vs AI分析 对比结果\n" + "=" * 60 + "\n\n"
        
        gz_category = gz_result.get('final_category', 'N/A') 
        ai_category_roman = ai_result.get('完整性类别', 'N/A') 
        
        ai_category_display_chinese = self.roman_to_chinese_display_map.get(ai_category_roman, ai_category_roman)
        ai_decision_display_for_main_line = f"{ai_category_roman} ({ai_category_display_chinese})"

        comparison += f"GZ传统方法判定: {gz_category}\n"
        comparison += f"AI分析判定: {ai_decision_display_for_main_line}\n"
        
        comparison += f"判定一致性: {'[SUCCESS] 一致' if gz_category == ai_category_display_chinese else '不一致'}\n\n"
        
        ai_confidence = ai_result.get('ai_confidence', 0.0)
        comparison += f"AI置信度: {ai_confidence:.2%}\n\n详细对比分析:\n" + "-" * 40 + "\n"
        
        if gz_category == ai_category_display_chinese:
            comparison += f"[SUCCESS] 两种方法均判定为 {gz_category}，结果一致。\n"
            if ai_confidence > 0.8: comparison += "[SUCCESS] AI分析置信度较高，结果可信度强。\n"
            elif ai_confidence > 0.6: comparison += "[WARNING] AI分析置信度中等，建议结合传统方法综合判断。\n"
            else: comparison += "[WARNING] AI分析置信度较低，建议以传统方法为准。\n"
        else:
            comparison += f"判定结果不一致：GZ方法为 {gz_category}，AI方法为 {ai_category_display_chinese} (内部罗马值为: {ai_category_roman})。\n建议进一步分析原因：\n1. 检查数据质量和完整性\n2. 验证GZ方法参数设置\n3. 考虑AI模型的适用性\n4. 结合工程经验进行综合判断\n"
        
        comparison += f"\nGZ传统方法详情:\n"
        k_values = gz_result.get('K_values', {})
        if k_values:
            k_counts = {}; 
            for k_val in k_values.values(): k_counts[k_val] = k_counts.get(k_val, 0) + 1
            for k_val in sorted(k_counts.keys()):
                count = k_counts[k_val]; percentage = (count / len(k_values)) * 100
                comparison += f"  K={k_val}: {count}个截面 ({percentage:.1f}%)\n"
        
        comparison += f"\nAI分析详情:\n"
        comparison += f"  AI判定类别 (显示): {ai_category_display_chinese}\n" 
        comparison += f"  AI判定类别 (内部罗马值): {ai_category_roman}\n" 
        comparison += f"  AI置信度: {ai_confidence:.2%}\n"

        class_probs_roman_keys = ai_result.get('class_probabilities', {}) 
        if class_probs_roman_keys:
            comparison += f"  各类别概率分布:\n"
            sorted_probs = sorted(class_probs_roman_keys.items(), key=lambda item: (item[1], item[0]), reverse=True)

            for class_key_roman, prob in sorted_probs:
                display_class_name_chinese = self.roman_to_chinese_display_map.get(class_key_roman, class_key_roman) 
                comparison += f"    {display_class_name_chinese}: {prob:.2%}\n"
        else:
            comparison += "  各类别概率分布: 无数据\n"
            
        comparison += f"\n分析建议:\n"
        gz_simple_category_roman = 'N/A'
        if isinstance(gz_category, str):
            if "I类桩" == gz_category: gz_simple_category_roman = "I"
            elif "II类桩" == gz_category: gz_simple_category_roman = "II"
            elif "III类桩" == gz_category: gz_simple_category_roman = "III"
            elif "IV类桩" == gz_category: gz_simple_category_roman = "IV"
        
        if gz_simple_category_roman != 'N/A' and ai_category_roman in ['I', 'II', 'III', 'IV']: 
            if gz_simple_category_roman == ai_category_roman: 
                comparison += "\n判定一致性: [SUCCESS] 一致 (基于内部罗马值比较)\n"
                if ai_confidence > 0.7:
                    comparison += "  建议: 建议采用一致的判定结果。\n"
                else:
                    comparison += "  建议: [WARNING] 虽然结果一致，但AI置信度不高，建议以GZ传统方法为主。\n"
            else:
                comparison += "\n判定一致性: 不一致 (基于内部罗马值比较)\n"
                comparison += "  建议 (重申不一致): \n   1. 优先考虑GZ传统方法结果\n   2. 分析数据质量和模型适用性\n   3. 结合现场实际情况综合判断\n"
        elif ai_category_roman == 'N/A':
             comparison += "\n判定一致性: [WARNING] AI结果为N/A，无法准确比较。\n"
        else: 
            comparison += f"\n判定一致性: [WARNING] 无法准确比较 (AI结果 '{ai_category_roman}' 非标准罗马数字类别)。\n"
            comparison += "  建议: 请确保GZ和AI分析均已执行并有有效结果，且AI模型输出可被映射为标准类别。\n"
        return comparison

    def display_comparison_result(self, comparison_text):
        print("[DISPLAY] Displaying comparison result...")
        self.comparison_text.delete(1.0, tk.END); self.comparison_text.insert(tk.END, comparison_text)
        for i in range(self.notebook.index("end")):
            if self.notebook.tab(i, "text") == "[COMPARE] Comparison": self.notebook.select(i); print("[CLIPBOARD] Auto-switched to Comparison tab"); break
        self.comparison_text.see(1.0); print("[SUCCESS] Comparison result displayed successfully")

    def plot_speed_amp(self):
        """绘制声速和波幅数据"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return
        try:
            self.fig.clear(); ax1 = self.fig.add_subplot(2, 1, 1); ax2 = self.fig.add_subplot(2, 1, 2)

            plot_cols_s = [col for col in ['S1', 'S2', 'S3'] if col in self.data_df.columns]
            plot_cols_a = [col for col in ['A1', 'A2', 'A3'] if col in self.data_df.columns]
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']  # 更好的颜色
            profile_names = ['剖面1-2', '剖面1-3', '剖面2-3']

            for i, col_s in enumerate(plot_cols_s):
                ax1.plot(self.data_df['Depth'], self.data_df[col_s], color=colors[i % len(colors)],
                        label=profile_names[i], linewidth=2, marker='o', markersize=3, alpha=0.8)

            ax1.set_xlabel('深度 (m)', fontsize=12); ax1.set_ylabel('相对声速 (%)', fontsize=12)
            ax1.set_title('相对声速随深度变化', fontsize=14, fontweight='bold')
            if plot_cols_s: ax1.legend(fontsize=10);
            ax1.grid(True, alpha=0.3); ax1.invert_yaxis()

            for i, col_a in enumerate(plot_cols_a):
                ax2.plot(self.data_df['Depth'], self.data_df[col_a], color=colors[i % len(colors)],
                        label=profile_names[i], linewidth=2, marker='s', markersize=3, alpha=0.8)

            ax2.set_xlabel('深度 (m)', fontsize=12); ax2.set_ylabel('波幅 (dB)', fontsize=12)
            ax2.set_title('波幅随深度变化', fontsize=14, fontweight='bold')
            if plot_cols_a: ax2.legend(fontsize=10);
            ax2.grid(True, alpha=0.3); ax2.invert_yaxis()

            self.fig.tight_layout(); self.canvas.draw(); print("Speed & Amplitude plot generated successfully")
        except Exception as e: print(f"Plot speed & amplitude error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot speed & amplitude: {str(e)}")

    def plot_speed_data(self):
        """绘制声速数据"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        speed_cols = [col for col in ['S1', 'S2', 'S3'] if col in self.data_df.columns]
        if not speed_cols:
            messagebox.showwarning("Warning", "No speed data found in the dataset"); return

        try:
            self.fig.clear(); ax = self.fig.add_subplot(1, 1, 1)

            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']  # 蓝、橙、绿
            profile_names = ['剖面1-2', '剖面1-3', '剖面2-3']

            for i, col_s in enumerate(speed_cols):
                ax.plot(self.data_df['Depth'], self.data_df[col_s], color=colors[i % len(colors)],
                       label=profile_names[i], linewidth=2.5, marker='o', markersize=4, alpha=0.8)

            ax.set_xlabel('深度 (m)', fontsize=12); ax.set_ylabel('相对声速 (%)', fontsize=12)
            ax.set_title('相对声速随深度变化', fontsize=16, fontweight='bold')
            ax.legend(loc='upper right', fontsize=11); ax.grid(True, alpha=0.3); ax.invert_yaxis()

            self.fig.tight_layout(); self.canvas.draw(); print("Speed data plot generated successfully")
        except Exception as e: print(f"Plot speed data error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot speed data: {str(e)}")

    def plot_amplitude_data(self):
        """绘制波幅数据"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        amp_cols = [col for col in ['A1', 'A2', 'A3'] if col in self.data_df.columns]
        if not amp_cols:
            messagebox.showwarning("Warning", "No amplitude data found in the dataset"); return

        try:
            self.fig.clear(); ax = self.fig.add_subplot(1, 1, 1)

            colors = ['#d62728', '#ff7f0e', '#2ca02c']  # 红、橙、绿
            profile_names = ['剖面1-2', '剖面1-3', '剖面2-3']

            for i, col_a in enumerate(amp_cols):
                ax.plot(self.data_df['Depth'], self.data_df[col_a], color=colors[i % len(colors)],
                       label=profile_names[i], linewidth=2.5, marker='s', markersize=4, alpha=0.8)

            ax.set_xlabel('深度 (m)', fontsize=12); ax.set_ylabel('波幅 (dB)', fontsize=12)
            ax.set_title('波幅随深度变化', fontsize=16, fontweight='bold')
            ax.legend(loc='upper right', fontsize=11); ax.grid(True, alpha=0.3); ax.invert_yaxis()

            self.fig.tight_layout(); self.canvas.draw(); print("Amplitude data plot generated successfully")
        except Exception as e: print(f"Plot amplitude data error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot amplitude data: {str(e)}")

    def plot_energy_data(self):
        """绘制能量数据"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        energy_cols = [col for col in ['E1', 'E2', 'E3'] if col in self.data_df.columns]
        if not energy_cols:
            messagebox.showwarning("Warning", "No energy data found in the dataset"); return

        try:
            self.fig.clear(); ax = self.fig.add_subplot(1, 1, 1)

            colors = ['#d62728', '#ff7f0e', '#2ca02c']  # 能量用暖色调
            profile_names = ['剖面1-2', '剖面1-3', '剖面2-3']

            for i, col_e in enumerate(energy_cols):
                ax.plot(self.data_df['Depth'], self.data_df[col_e], color=colors[i % len(colors)],
                       label=profile_names[i], linewidth=2.5, marker='^', markersize=4, alpha=0.8)

            ax.set_xlabel('深度 (m)', fontsize=12); ax.set_ylabel('能量 (%)', fontsize=12)
            ax.set_title('能量随深度变化', fontsize=16, fontweight='bold')
            ax.legend(loc='upper right', fontsize=11); ax.grid(True, alpha=0.3); ax.invert_yaxis()

            self.fig.tight_layout(); self.canvas.draw(); print("Energy plot generated successfully")
        except Exception as e: print(f"Plot energy error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot energy data: {str(e)}")

    def plot_psd_data(self):
        """绘制PSD数据"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        psd_cols = [col for col in ['P1', 'P2', 'P3'] if col in self.data_df.columns]
        if not psd_cols:
            messagebox.showwarning("Warning", "No PSD data found in the dataset"); return

        try:
            self.fig.clear(); ax = self.fig.add_subplot(1, 1, 1)

            colors = ['#9467bd', '#8c564b', '#e377c2']  # PSD用紫色调
            profile_names = ['剖面1-2', '剖面1-3', '剖面2-3']

            for i, col_p in enumerate(psd_cols):
                ax.plot(self.data_df['Depth'], self.data_df[col_p], color=colors[i % len(colors)],
                       label=profile_names[i], linewidth=2.5, marker='D', markersize=4, alpha=0.8)

            ax.set_xlabel('深度 (m)', fontsize=12); ax.set_ylabel('功率谱密度', fontsize=12)
            ax.set_title('功率谱密度随深度变化', fontsize=16, fontweight='bold')
            ax.legend(loc='upper right', fontsize=11); ax.grid(True, alpha=0.3); ax.invert_yaxis()

            self.fig.tight_layout(); self.canvas.draw(); print("PSD plot generated successfully")
        except Exception as e: print(f"Plot PSD error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot PSD data: {str(e)}")

    def plot_all_data(self):
        """绘制所有可用数据的综合图表"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        try:
            self.fig.clear()

            # 检查可用的数据类型
            has_speed = any(col in self.data_df.columns for col in ['S1', 'S2', 'S3'])
            has_amp = any(col in self.data_df.columns for col in ['A1', 'A2', 'A3'])
            has_energy = any(col in self.data_df.columns for col in ['E1', 'E2', 'E3'])
            has_psd = any(col in self.data_df.columns for col in ['P1', 'P2', 'P3'])

            # 确定子图布局
            subplot_count = sum([has_speed, has_amp, has_energy, has_psd])
            if subplot_count == 0:
                messagebox.showwarning("Warning", "No plottable data found"); return

            # 创建子图
            if subplot_count <= 2:
                rows, cols = subplot_count, 1
            else:
                rows, cols = 2, 2

            plot_idx = 1
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
            profile_names = ['剖面1-2', '剖面1-3', '剖面2-3']

            # 绘制声速数据
            if has_speed:
                ax = self.fig.add_subplot(rows, cols, plot_idx)
                plot_cols_s = [col for col in ['S1', 'S2', 'S3'] if col in self.data_df.columns]
                for i, col_s in enumerate(plot_cols_s):
                    ax.plot(self.data_df['Depth'], self.data_df[col_s], color=colors[i],
                           label=profile_names[i], linewidth=2, marker='o', markersize=2)
                ax.set_xlabel('深度 (m)'); ax.set_ylabel('相对声速 (%)')
                ax.set_title('相对声速', fontweight='bold')
                ax.legend(fontsize=9); ax.grid(True, alpha=0.3); ax.invert_yaxis()
                plot_idx += 1

            # 绘制波幅数据
            if has_amp:
                ax = self.fig.add_subplot(rows, cols, plot_idx)
                plot_cols_a = [col for col in ['A1', 'A2', 'A3'] if col in self.data_df.columns]
                for i, col_a in enumerate(plot_cols_a):
                    ax.plot(self.data_df['Depth'], self.data_df[col_a], color=colors[i],
                           label=profile_names[i], linewidth=2, marker='s', markersize=2)
                ax.set_xlabel('深度 (m)'); ax.set_ylabel('波幅 (dB)')
                ax.set_title('波幅', fontweight='bold')
                ax.legend(fontsize=9); ax.grid(True, alpha=0.3); ax.invert_yaxis()
                plot_idx += 1

            # 绘制能量数据
            if has_energy:
                ax = self.fig.add_subplot(rows, cols, plot_idx)
                plot_cols_e = [col for col in ['E1', 'E2', 'E3'] if col in self.data_df.columns]
                energy_colors = ['#d62728', '#ff7f0e', '#2ca02c']
                for i, col_e in enumerate(plot_cols_e):
                    ax.plot(self.data_df['Depth'], self.data_df[col_e], color=energy_colors[i],
                           label=profile_names[i], linewidth=2, marker='^', markersize=2)
                ax.set_xlabel('深度 (m)'); ax.set_ylabel('能量 (%)')
                ax.set_title('能量', fontweight='bold')
                ax.legend(fontsize=9); ax.grid(True, alpha=0.3); ax.invert_yaxis()
                plot_idx += 1

            # 绘制PSD数据
            if has_psd:
                ax = self.fig.add_subplot(rows, cols, plot_idx)
                plot_cols_p = [col for col in ['P1', 'P2', 'P3'] if col in self.data_df.columns]
                psd_colors = ['#9467bd', '#8c564b', '#e377c2']
                for i, col_p in enumerate(plot_cols_p):
                    ax.plot(self.data_df['Depth'], self.data_df[col_p], color=psd_colors[i],
                           label=profile_names[i], linewidth=2, marker='D', markersize=2)
                ax.set_xlabel('深度 (m)'); ax.set_ylabel('功率谱密度')
                ax.set_title('功率谱密度', fontweight='bold')
                ax.legend(fontsize=9); ax.grid(True, alpha=0.3); ax.invert_yaxis()

            self.fig.suptitle('桩基完整性检测数据综合分析', fontsize=16, fontweight='bold')
            self.fig.tight_layout(); self.canvas.draw(); print("All data plot generated successfully")
        except Exception as e: print(f"Plot all data error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot all data: {str(e)}")

    def plot_analysis_results(self):
        if 'gz_traditional' not in self.analysis_results: messagebox.showwarning("Warning", "Please run GZ traditional analysis first"); return
        try:
            self.fig.clear(); gz_result = self.analysis_results['gz_traditional']; K_values = gz_result.get('K_values', {})
            if not K_values: messagebox.showwarning("Warning", "No K values to plot"); return
            ax = self.fig.add_subplot(1, 1, 1); depths = sorted(K_values.keys()); k_vals = [K_values[d] for d in depths]
            colors_map = {1: 'green', 2: 'yellow', 3: 'orange', 4: 'red'}; point_colors = [colors_map.get(k, 'gray') for k in k_vals]
            ax.scatter(k_vals, depths, c=point_colors, s=50, alpha=0.7)
            for k_cat in [1, 2, 3, 4]: 
                k_depths = [d for d, k_val in K_values.items() if k_val == k_cat]
                if k_depths:
                    for depth_val in k_depths: 
                        ax.axhline(y=depth_val, color=colors_map[k_cat], alpha=0.3, linewidth=1)
            ax.set_xlabel('K值'); ax.set_ylabel('深度 (m)'); ax.set_title(f'K值分布图 - 最终判定: {gz_result.get("final_category", "N/A")}')
            ax.set_xticks([1, 2, 3, 4]); ax.grid(True, alpha=0.3); ax.invert_yaxis()
            legend_elements = [plt.Line2D([0], [0], marker='o', color='w', markerfacecolor=colors_map[k_cat_legend], markersize=8, label=f'K={k_cat_legend}') for k_cat_legend in [1,2,3,4]] 
            ax.legend(handles=legend_elements, loc='upper right')
            self.fig.tight_layout(); self.canvas.draw(); print("Analysis results plot generated successfully")
        except Exception as e: print(f"Plot analysis results error: {str(e)}"); messagebox.showerror("Error", f"Failed to plot analysis results: {str(e)}")

    def plot_contour_data(self):
        """绘制云图（等高线图）显示各指标在深度与剖面上的分布"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        try:
            # 检查可用的数据类型
            has_speed = any(col in self.data_df.columns for col in ['S1', 'S2', 'S3'])
            has_amp = any(col in self.data_df.columns for col in ['A1', 'A2', 'A3'])
            has_energy = any(col in self.data_df.columns for col in ['E1', 'E2', 'E3'])
            has_psd = any(col in self.data_df.columns for col in ['P1', 'P2', 'P3'])

            available_data = []
            if has_speed: available_data.append(('Speed', ['S1', 'S2', 'S3'], '相对声速 (%)', 'viridis'))
            if has_amp: available_data.append(('Amplitude', ['A1', 'A2', 'A3'], '波幅 (dB)', 'plasma'))
            if has_energy: available_data.append(('Energy', ['E1', 'E2', 'E3'], '能量 (%)', 'inferno'))
            if has_psd: available_data.append(('PSD', ['P1', 'P2', 'P3'], '功率谱密度', 'cividis'))

            if not available_data:
                messagebox.showwarning("Warning", "No data available for contour plotting"); return

            self.fig.clear()

            # 确定子图布局
            n_plots = len(available_data)
            if n_plots == 1:
                rows, cols = 1, 1
            elif n_plots == 2:
                rows, cols = 1, 2
            elif n_plots <= 4:
                rows, cols = 2, 2
            else:
                rows, cols = 3, 2

            for idx, (data_type, columns, ylabel, colormap) in enumerate(available_data):
                ax = self.fig.add_subplot(rows, cols, idx + 1)

                # 准备数据
                depths = self.data_df['Depth'].values
                profiles = np.array([1, 2, 3])  # 三个剖面

                # 创建网格数据
                depth_grid, profile_grid = np.meshgrid(depths, profiles)

                # 准备Z值矩阵
                z_values = np.zeros((len(profiles), len(depths)))
                for i, col in enumerate(columns):
                    if col in self.data_df.columns:
                        z_values[i, :] = self.data_df[col].values

                # 创建等高线图
                contour = ax.contourf(depth_grid, profile_grid, z_values, levels=20, cmap=colormap, alpha=0.8)

                # 添加等高线
                contour_lines = ax.contour(depth_grid, profile_grid, z_values, levels=10, colors='black', alpha=0.4, linewidths=0.5)
                ax.clabel(contour_lines, inline=True, fontsize=8, fmt='%.1f')

                # 添加数据点
                for i, col in enumerate(columns):
                    if col in self.data_df.columns:
                        ax.scatter(depths, np.full_like(depths, profiles[i]),
                                 c=self.data_df[col].values, cmap=colormap, s=20, alpha=0.9, edgecolors='white', linewidth=0.5)

                # 设置坐标轴
                ax.set_xlabel('深度 (m)', fontsize=10)
                ax.set_ylabel('剖面', fontsize=10)
                ax.set_title(f'{data_type} 云图分布', fontsize=12, fontweight='bold')
                ax.set_yticks([1, 2, 3])
                ax.set_yticklabels(['剖面1-2', '剖面1-3', '剖面2-3'])
                ax.grid(True, alpha=0.3)

                # 添加颜色条
                cbar = self.fig.colorbar(contour, ax=ax, shrink=0.8)
                cbar.set_label(ylabel, fontsize=9)

                # 反转y轴使深度从上到下增加
                ax.invert_xaxis()

            self.fig.suptitle('桩基完整性检测数据云图分析', fontsize=16, fontweight='bold')
            self.fig.tight_layout()
            self.canvas.draw()
            print("Contour plots generated successfully")

        except Exception as e:
            print(f"Plot contour error: {str(e)}")
            messagebox.showerror("Error", f"Failed to generate contour plots: {str(e)}")

    def plot_speed_contour(self):
        """绘制声速云图"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        speed_cols = [col for col in ['S1', 'S2', 'S3'] if col in self.data_df.columns]
        if not speed_cols:
            messagebox.showwarning("Warning", "No speed data found in the dataset"); return

        try:
            self.fig.clear(); ax = self.fig.add_subplot(1, 1, 1)

            depths = self.data_df['Depth'].values
            profiles = np.array([1, 2, 3])
            depth_grid, profile_grid = np.meshgrid(depths, profiles)

            z_values = np.zeros((len(profiles), len(depths)))
            for i, col in enumerate(speed_cols):
                if col in self.data_df.columns:
                    z_values[i, :] = self.data_df[col].values

            contour = ax.contourf(depth_grid, profile_grid, z_values, levels=20, cmap='viridis', alpha=0.8)
            contour_lines = ax.contour(depth_grid, profile_grid, z_values, levels=10, colors='black', alpha=0.4, linewidths=0.5)
            ax.clabel(contour_lines, inline=True, fontsize=8, fmt='%.1f')

            for i, col in enumerate(speed_cols):
                if col in self.data_df.columns:
                    ax.scatter(depths, np.full_like(depths, profiles[i]),
                             c=self.data_df[col].values, cmap='viridis', s=20, alpha=0.9, edgecolors='white', linewidth=0.5)

            ax.set_xlabel('深度 (m)', fontsize=12); ax.set_ylabel('剖面', fontsize=12)
            ax.set_title('相对声速云图分布', fontsize=16, fontweight='bold')
            ax.set_yticks([1, 2, 3]); ax.set_yticklabels(['剖面1-2', '剖面1-3', '剖面2-3'])
            ax.grid(True, alpha=0.3); ax.invert_xaxis()

            cbar = self.fig.colorbar(contour, ax=ax, shrink=0.8)
            cbar.set_label('相对声速 (%)', fontsize=11)

            self.fig.tight_layout(); self.canvas.draw(); print("Speed contour plot generated successfully")
        except Exception as e: print(f"Plot speed contour error: {str(e)}"); messagebox.showerror("Error", f"Failed to generate speed contour plot: {str(e)}")

    def plot_amplitude_contour(self):
        """绘制波幅云图"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        amp_cols = [col for col in ['A1', 'A2', 'A3'] if col in self.data_df.columns]
        if not amp_cols:
            messagebox.showwarning("Warning", "No amplitude data found in the dataset"); return

        try:
            self.fig.clear(); ax = self.fig.add_subplot(1, 1, 1)

            depths = self.data_df['Depth'].values
            profiles = np.array([1, 2, 3])
            depth_grid, profile_grid = np.meshgrid(depths, profiles)

            z_values = np.zeros((len(profiles), len(depths)))
            for i, col in enumerate(amp_cols):
                if col in self.data_df.columns:
                    z_values[i, :] = self.data_df[col].values

            contour = ax.contourf(depth_grid, profile_grid, z_values, levels=20, cmap='plasma', alpha=0.8)
            contour_lines = ax.contour(depth_grid, profile_grid, z_values, levels=10, colors='black', alpha=0.4, linewidths=0.5)
            ax.clabel(contour_lines, inline=True, fontsize=8, fmt='%.1f')

            for i, col in enumerate(amp_cols):
                if col in self.data_df.columns:
                    ax.scatter(depths, np.full_like(depths, profiles[i]),
                             c=self.data_df[col].values, cmap='plasma', s=20, alpha=0.9, edgecolors='white', linewidth=0.5)

            ax.set_xlabel('深度 (m)', fontsize=12); ax.set_ylabel('剖面', fontsize=12)
            ax.set_title('波幅云图分布', fontsize=16, fontweight='bold')
            ax.set_yticks([1, 2, 3]); ax.set_yticklabels(['剖面1-2', '剖面1-3', '剖面2-3'])
            ax.grid(True, alpha=0.3); ax.invert_xaxis()

            cbar = self.fig.colorbar(contour, ax=ax, shrink=0.8)
            cbar.set_label('波幅 (dB)', fontsize=11)

            self.fig.tight_layout(); self.canvas.draw(); print("Amplitude contour plot generated successfully")
        except Exception as e: print(f"Plot amplitude contour error: {str(e)}"); messagebox.showerror("Error", f"Failed to generate amplitude contour plot: {str(e)}")

    def plot_energy_contour(self):
        """绘制能量云图"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        energy_cols = [col for col in ['E1', 'E2', 'E3'] if col in self.data_df.columns]
        if not energy_cols:
            messagebox.showwarning("Warning", "No energy data found in the dataset"); return

        try:
            self.fig.clear(); ax = self.fig.add_subplot(1, 1, 1)

            depths = self.data_df['Depth'].values
            profiles = np.array([1, 2, 3])
            depth_grid, profile_grid = np.meshgrid(depths, profiles)

            z_values = np.zeros((len(profiles), len(depths)))
            for i, col in enumerate(energy_cols):
                if col in self.data_df.columns:
                    z_values[i, :] = self.data_df[col].values

            contour = ax.contourf(depth_grid, profile_grid, z_values, levels=20, cmap='inferno', alpha=0.8)
            contour_lines = ax.contour(depth_grid, profile_grid, z_values, levels=10, colors='black', alpha=0.4, linewidths=0.5)
            ax.clabel(contour_lines, inline=True, fontsize=8, fmt='%.2f')

            for i, col in enumerate(energy_cols):
                if col in self.data_df.columns:
                    ax.scatter(depths, np.full_like(depths, profiles[i]),
                             c=self.data_df[col].values, cmap='inferno', s=20, alpha=0.9, edgecolors='white', linewidth=0.5)

            ax.set_xlabel('深度 (m)', fontsize=12); ax.set_ylabel('剖面', fontsize=12)
            ax.set_title('能量云图分布', fontsize=16, fontweight='bold')
            ax.set_yticks([1, 2, 3]); ax.set_yticklabels(['剖面1-2', '剖面1-3', '剖面2-3'])
            ax.grid(True, alpha=0.3); ax.invert_xaxis()

            cbar = self.fig.colorbar(contour, ax=ax, shrink=0.8)
            cbar.set_label('能量 (%)', fontsize=11)

            self.fig.tight_layout(); self.canvas.draw(); print("Energy contour plot generated successfully")
        except Exception as e: print(f"Plot energy contour error: {str(e)}"); messagebox.showerror("Error", f"Failed to generate energy contour plot: {str(e)}")

    def plot_psd_contour(self):
        """绘制PSD云图"""
        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("Warning", "Please load data first"); return

        psd_cols = [col for col in ['P1', 'P2', 'P3'] if col in self.data_df.columns]
        if not psd_cols:
            messagebox.showwarning("Warning", "No PSD data found in the dataset"); return

        try:
            self.fig.clear(); ax = self.fig.add_subplot(1, 1, 1)

            depths = self.data_df['Depth'].values
            profiles = np.array([1, 2, 3])
            depth_grid, profile_grid = np.meshgrid(depths, profiles)

            z_values = np.zeros((len(profiles), len(depths)))
            for i, col in enumerate(psd_cols):
                if col in self.data_df.columns:
                    z_values[i, :] = self.data_df[col].values

            contour = ax.contourf(depth_grid, profile_grid, z_values, levels=20, cmap='cividis', alpha=0.8)
            contour_lines = ax.contour(depth_grid, profile_grid, z_values, levels=10, colors='black', alpha=0.4, linewidths=0.5)
            ax.clabel(contour_lines, inline=True, fontsize=8, fmt='%.2f')

            for i, col in enumerate(psd_cols):
                if col in self.data_df.columns:
                    ax.scatter(depths, np.full_like(depths, profiles[i]),
                             c=self.data_df[col].values, cmap='cividis', s=20, alpha=0.9, edgecolors='white', linewidth=0.5)

            ax.set_xlabel('深度 (m)', fontsize=12); ax.set_ylabel('剖面', fontsize=12)
            ax.set_title('功率谱密度云图分布', fontsize=16, fontweight='bold')
            ax.set_yticks([1, 2, 3]); ax.set_yticklabels(['剖面1-2', '剖面1-3', '剖面2-3'])
            ax.grid(True, alpha=0.3); ax.invert_xaxis()

            cbar = self.fig.colorbar(contour, ax=ax, shrink=0.8)
            cbar.set_label('功率谱密度', fontsize=11)

            self.fig.tight_layout(); self.canvas.draw(); print("PSD contour plot generated successfully")
        except Exception as e: print(f"Plot PSD contour error: {str(e)}"); messagebox.showerror("Error", f"Failed to generate PSD contour plot: {str(e)}")

    def save_plot(self):
        try:
            file_path = filedialog.asksaveasfilename(title="Save Plot", defaultextension=".png", filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("SVG files", "*.svg"), ("All files", "*.*")])
            if file_path: self.fig.savefig(file_path, dpi=300, bbox_inches='tight'); messagebox.showinfo("Success", f"Plot saved to {file_path}"); print(f"[SUCCESS] Plot saved to: {file_path}")
        except Exception as e: print(f"[ERROR] Save plot error: {str(e)}"); messagebox.showerror("Error", f"Failed to save plot: {str(e)}")

    def save_config(self):
        try:
            config_data = {key: var.get() for key, var in self.config_vars.items()}
            file_path = filedialog.asksaveasfilename(title="Save Configuration", defaultextension=".json", filetypes=[("JSON files", "*.json"), ("All files", "*.*")])
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f: json.dump(config_data, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("Success", f"Configuration saved to {file_path}"); print(f"[SUCCESS] Configuration saved to: {file_path}")
        except Exception as e: print(f"[ERROR] Save config error: {str(e)}"); messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

    def load_config(self):
        try:
            file_path = filedialog.askopenfilename(title="Load Configuration", filetypes=[("JSON files", "*.json"), ("All files", "*.*")])
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f: config_data = json.load(f)
                for key, value in config_data.items():
                    if key in self.config_vars: self.config_vars[key].set(value)
                messagebox.showinfo("Success", f"Configuration loaded from {file_path}"); print(f"[SUCCESS] Configuration loaded from: {file_path}")
        except Exception as e: print(f"[ERROR] Load config error: {str(e)}"); messagebox.showerror("Error", f"Failed to load configuration: {str(e)}")

    def reset_config(self):
        try:
            self.config_vars['sp_ge_100'].set(100.0); self.config_vars['sp_85_lt_100_min'].set(85.0); self.config_vars['sp_85_lt_100_max'].set(100.0); self.config_vars['sp_75_lt_85_min'].set(75.0); self.config_vars['sp_75_lt_85_max'].set(85.0); self.config_vars['sp_65_lt_75_min'].set(65.0); self.config_vars['sp_65_lt_75_max'].set(75.0)
            self.config_vars['ad_le_0'].set(0.0); self.config_vars['ad_gt_0_le_4_min'].set(0.0); self.config_vars['ad_gt_0_le_4_max'].set(4.0); self.config_vars['ad_gt_4_le_8_min'].set(4.0); self.config_vars['ad_gt_4_le_8_max'].set(8.0); self.config_vars['ad_gt_8_le_12_min'].set(8.0); self.config_vars['ad_gt_8_le_12_max'].set(12.0)
            self.config_vars['bi_ratio_default'].set(1.0); self.config_vars['auto_analysis'].set(True); self.config_vars['show_details'].set(True)
            messagebox.showinfo("Success", "Configuration reset to defaults"); print("[SUCCESS] Configuration reset to defaults")
        except Exception as e: print(f"[ERROR] Reset config error: {str(e)}"); messagebox.showerror("Error", f"Failed to reset configuration: {str(e)}")

    def save_results(self):
        if not self.analysis_results: messagebox.showwarning("Warning", "No analysis results to save"); return
        try:
            file_path = filedialog.asksaveasfilename(title="Save Analysis Results", defaultextension=".txt", filetypes=[("Text files", "*.txt"), ("JSON files", "*.json"), ("All files", "*.*")])
            if file_path:
                if file_path.endswith('.json'):
                    with open(file_path, 'w', encoding='utf-8') as f: 
                        json.dump(self.analysis_results, f, indent=2, ensure_ascii=False, default=str)
                else: 
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write("桩基完整性分析结果报告\n" + "=" * 50 + "\n\n")
                        if 'gz_traditional' in self.analysis_results: 
                            f.write("GZ传统方法分析结果:\n" + "-" * 30 + "\n" + self.analysis_results['gz_traditional'].get('analysis_summary', '') + "\n\n")
                        if 'ai' in self.analysis_results:
                            ai_res = self.analysis_results['ai']
                            ai_category_roman_save = ai_res.get('完整性类别', 'N/A') 
                            category_display_chinese_save = self.roman_to_chinese_display_map.get(ai_category_roman_save, ai_category_roman_save)
                            
                            f.write("AI分析结果:\n" + "-" * 30 + "\n")
                            f.write(f"桩基完整性类别: {category_display_chinese_save}\n") 
                            f.write(f"AI置信度: {ai_res.get('ai_confidence', 0.0):.2%}\n")
                            f.write(f"AI分析结论: {ai_res.get('overall_reasoning', '无分析结论')}\n\n")
                messagebox.showinfo("Success", f"Results saved to {file_path}"); print(f"Results saved to: {file_path}")
        except Exception as e: print(f"Save results error: {str(e)}"); messagebox.showerror("Error", f"Failed to save results: {str(e)}")

    def run(self):
        print("Starting Pile Integrity Analyzer (GZ Method) GUI..."); self.root.mainloop()

    def switch_ai_system(self):
        try:
            system_version = self.ai_system_var.get()
            if system_version == "v2" and self.use_v2_analyzer:
                self.v2_model_frame.pack(fill='x', pady=(0, 15), padx=10)
                self.v1_model_frame.pack_forget(); print("Switching to AI system V2.0")
            else:
                self.v1_model_frame.pack(fill='x', pady=(0, 20), padx=10)
                if hasattr(self, 'v2_model_frame'): self.v2_model_frame.pack_forget()
                print("Switching to AI system V1.0 (or V2 not available)")
        except Exception as e: print(f"Failed to switch AI system: {e}")

    def refresh_model_list(self): 
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            models = self.ai_analyzer_v2.model_manager.get_available_models()
            model_names = []; self.model_key_mapping = {}
            for key, model_info in models.items():
                display_name = f"{model_info.name} ({model_info.accuracy:.1%})"
                model_names.append(display_name); self.model_key_mapping[display_name] = key
            self.model_combobox['values'] = model_names
            current_model_info = self.ai_analyzer_v2.model_manager.get_current_model_info()
            if current_model_info:
                current_display = f"{current_model_info.name} ({current_model_info.accuracy:.1%})"
                if current_display in model_names: self.selected_model_var.set(current_display)
            print(f"[REFRESH] 模型列表已刷新: {len(model_names)} 个模型")
        except Exception as e: print(f"[ERROR] 刷新模型列表失败: {e}")

    def refresh_extractor_list(self): 
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            extractors = self.ai_analyzer_v2.extractor_manager.get_available_extractors()
            extractor_names = []; self.extractor_key_mapping = {}
            for key, extractor_info in extractors.items():
                display_name = f"{extractor_info['name']} ({extractor_info['feature_count']}特征)"
                extractor_names.append(display_name); self.extractor_key_mapping[display_name] = key
            self.extractor_combobox['values'] = extractor_names
            current_extractor = self.ai_analyzer_v2.extractor_manager.current_extractor
            if current_extractor:
                current_display = f"{current_extractor.name} ({current_extractor.feature_count}特征)"
                if current_display in extractor_names: self.selected_extractor_var.set(current_display)
            print(f"[REFRESH] 特征提取器列表已刷新: {len(extractor_names)} 个提取器")
        except Exception as e: print(f"[ERROR] 刷新特征提取器列表失败: {e}")

    def on_model_selected(self, event=None): 
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            selected_display = self.selected_model_var.get()
            if selected_display in self.model_key_mapping:
                model_key = self.model_key_mapping[selected_display]
                if self.ai_analyzer_v2.set_model(model_key):
                    print(f"[SUCCESS] 模型已选择 (V2): {selected_display}"); self.update_model_info_display()
                else: print(f"[ERROR] 模型选择失败 (V2): {selected_display}")
        except Exception as e: print(f"[ERROR] 模型选择处理失败 (V2): {e}")

    def on_extractor_selected(self, event=None): 
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            selected_display = self.selected_extractor_var.get()
            if selected_display in self.extractor_key_mapping:
                extractor_key = self.extractor_key_mapping[selected_display]
                if self.ai_analyzer_v2.set_feature_extractor(extractor_key):
                    print(f"[SUCCESS] 特征提取器已选择 (V2): {selected_display}"); self.update_model_info_display()
                else: print(f"[ERROR] 特征提取器选择失败 (V2): {selected_display}")
        except Exception as e: print(f"[ERROR] 特征提取器选择处理失败 (V2): {e}")

    def update_model_info_display(self): 
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2): return
            for widget in self.model_info_frame.winfo_children(): widget.destroy()
            current_model_info = self.ai_analyzer_v2.model_manager.get_current_model_info()
            current_extractor = self.ai_analyzer_v2.extractor_manager.current_extractor
            if current_model_info and current_extractor:
                info_text = f"[DATA] 当前配置:\n  • 模型: {current_model_info.name}\n  • 准确率: {current_model_info.accuracy:.1%}\n  • 特征提取器: {current_extractor.name}\n  • 特征数量: {current_extractor.feature_count}\n  • 模型类型: {current_model_info.model_type}\n  • 文件大小: {current_model_info.file_size / 1024 / 1024:.1f} MB"
                ttk.Label(self.model_info_frame, text=info_text, font=('Consolas', 9), foreground='darkgreen').pack(anchor='w')
                if current_model_info.feature_count != current_extractor.feature_count:
                    warning_text = f"[WARNING] 警告: 模型期望 {current_model_info.feature_count} 个特征，但提取器提供 {current_extractor.feature_count} 个特征"
                    ttk.Label(self.model_info_frame, text=warning_text, font=('Consolas', 9), foreground='red').pack(anchor='w', pady=(5, 0))
                else: ttk.Label(self.model_info_frame, text="[SUCCESS] 模型与特征提取器兼容", font=('Consolas', 9), foreground='green').pack(anchor='w', pady=(5, 0))
        except Exception as e: print(f"[ERROR] 更新模型信息显示失败 (V2): {e}")

    def load_external_model_v2(self): 
        try:
            if not (self.use_v2_analyzer and self.ai_analyzer_v2):
                messagebox.showwarning("Warning", "请先切换到AI System V2.0 (如果可用)"); return
            default_model_dir = self.ai_models_dir
            file_path = filedialog.askopenfilename(title="选择AI模型文件 (V2)", filetypes=[("Pickle files", "*.pkl"), ("All files", "*.*")], initialdir=default_model_dir)
            if not file_path: return
            print(f"[LOAD] 用户选择模型文件 (V2): {file_path}")
            self._show_model_loading_dialog(file_path) 
        except Exception as e:
            print(f"[ERROR] 加载外部模型失败 (V2): {e}"); traceback.print_exc()
            messagebox.showerror("Error", f"加载外部模型失败 (V2): {str(e)}")

    def _show_model_loading_dialog(self, file_path): 
        try:
            dialog = tk.Toplevel(self.root); dialog.title("加载外部模型"); dialog.geometry("650x550"); dialog.resizable(True, True); dialog.transient(self.root); dialog.grab_set()
            dialog.update_idletasks(); x = (dialog.winfo_screenwidth() // 2) - (650 // 2); y = (dialog.winfo_screenheight() // 2) - (550 // 2); dialog.geometry(f"650x550+{x}+{y}")
            main_frame = ttk.Frame(dialog, padding="15"); main_frame.pack(fill='both', expand=True)
            main_frame.grid_rowconfigure(3, weight=1); main_frame.grid_columnconfigure(0, weight=1)
            ttk.Label(main_frame, text="[AI] 加载外部AI模型", font=('Segoe UI', 14, 'bold')).grid(row=0, column=0, pady=(0, 15), sticky='w')
            file_frame = ttk.LabelFrame(main_frame, text="[FILE] 文件信息", padding="10"); file_frame.grid(row=1, column=0, sticky='ew', pady=(0, 10))
            ttk.Label(file_frame, text=f"文件路径: {file_path}", wraplength=500).pack(anchor='w')
            ttk.Label(file_frame, text=f"文件大小: {os.path.getsize(file_path) / 1024 / 1024:.1f} MB").pack(anchor='w')
            name_frame = ttk.Frame(main_frame); name_frame.grid(row=2, column=0, sticky='ew', pady=(0, 10))
            ttk.Label(name_frame, text="模型名称:", font=('Segoe UI', 10, 'bold')).pack(anchor='w')
            model_name_var = tk.StringVar(value=f"外部模型 - {os.path.basename(file_path)}")
            ttk.Entry(name_frame, textvariable=model_name_var, width=60).pack(fill='x', pady=(5, 0))
            preview_frame = ttk.LabelFrame(main_frame, text="[SEARCH] 模型预览", padding="10"); preview_frame.grid(row=3, column=0, sticky='nsew', pady=(0, 10))
            preview_text = tk.Text(preview_frame, height=8, wrap='word', font=('Consolas', 9))
            preview_scrollbar = ttk.Scrollbar(preview_frame, orient='vertical', command=preview_text.yview); preview_text.configure(yscrollcommand=preview_scrollbar.set)
            preview_text.pack(side='left', fill='both', expand=True); preview_scrollbar.pack(side='right', fill='y')
            self._analyze_and_preview_model(file_path, preview_text) 
            ttk.Separator(main_frame, orient='horizontal').grid(row=4, column=0, sticky='ew', pady=(10, 10))
            button_frame = ttk.Frame(main_frame); button_frame.grid(row=5, column=0, sticky='ew', pady=(0, 5))
            def load_model_action(): 
                try:
                    model_name = model_name_var.get().strip()
                    if not model_name: messagebox.showwarning("Warning", "请输入模型名称"); return
                    print(f"[REFRESH] 开始加载模型 (V2): {model_name}")
                    if self.ai_analyzer_v2.model_manager.load_external_model(file_path, model_name):
                        print(f"[SUCCESS] 模型加载成功 (V2)，正在更新界面...")
                        self.refresh_model_list() 
                        models = self.ai_analyzer_v2.model_manager.get_available_models()
                        for key, model_info in models.items():
                            if model_info.name == model_name:
                                display_name = f"{model_info.name} ({model_info.accuracy:.1%})"
                                self.selected_model_var.set(display_name); self.on_model_selected(); break
                        messagebox.showinfo("Success", f"模型 '{model_name}' (V2) 加载成功！\n\n已自动选择该模型。")
                        dialog.destroy()
                    else: messagebox.showerror("Error", "模型加载失败 (V2)，请检查文件格式或控制台日志。")
                except Exception as ex: print(f"[ERROR] 模型加载失败 (V2): {ex}"); traceback.print_exc(); messagebox.showerror("Error", f"模型加载失败 (V2): {str(ex)}")
            load_btn = ttk.Button(button_frame, text="确定加载", style='Accent.TButton', command=load_model_action, width=15); load_btn.pack(side='right', padx=(10, 0))
            ttk.Button(button_frame, text="取消", style='Modern.TButton', command=dialog.destroy, width=15).pack(side='right')
            ttk.Label(button_frame, text="提示: 点击'确定加载'按钮完成模型加载", font=('Segoe UI', 9), foreground='gray').pack(side='left')
            load_btn.focus_set(); dialog.bind('<Return>', lambda e: load_model_action())
            dialog.update_idletasks()
        except Exception as e: print(f"[ERROR] 显示模型加载对话框失败: {e}"); messagebox.showerror("Error", f"显示模型加载对话框失败: {str(e)}")

    def setup_3d_visualization_menu(self):
        """设置3D可视化菜单"""
        try:
            # 在可视化标签页添加3D可视化按钮
            if hasattr(self, 'visualization_frame'):
                viz_control_frame = ttk.LabelFrame(self.visualization_frame, text="[DART] 3D可视化")
                viz_control_frame.pack(fill='x', pady=(0, 15), padx=10)

                button_frame = ttk.Frame(viz_control_frame)
                button_frame.pack(fill='x', padx=15, pady=15)

                ttk.Button(button_frame, text="[DATA] Speed% 3D可视化",
                          style='Modern.TButton',
                          command=lambda: self.plotly_visualization('speed')).pack(side='left', padx=(0, 10))

                ttk.Button(button_frame, text="[CHART] Amp% 3D可视化",
                          style='Modern.TButton',
                          command=lambda: self.plotly_visualization('amplitude')).pack(side='left', padx=(0, 10))

                ttk.Button(button_frame, text="[ENERGY] Energy% 3D可视化",
                          style='Modern.TButton',
                          command=lambda: self.plotly_visualization('energy')).pack(side='left', padx=(0, 10))

                ttk.Button(button_frame, text="[SIGNAL] PSD 3D可视化",
                          style='Modern.TButton',
                          command=lambda: self.plotly_visualization('psd')).pack(side='left')

                print("3D visualization menu setup completed")
        except Exception as e:
            print(f"Failed to setup 3D visualization menu: {e}")

    def plotly_visualization(self, data_type):
        """使用Plotly进行三维可视化"""
        if not PLOTLY_AVAILABLE:
            messagebox.showwarning("功能不可用", "Plotly库未安装，无法使用3D可视化功能。\n请运行: pip install plotly")
            return

        if self.data_df is None or self.data_df.empty:
            messagebox.showwarning("数据错误", "请先加载有效数据")
            return

        # 检查必要的数据列
        required_columns = ['Depth', 'S1', 'A1', 'S2', 'A2', 'S3', 'A3']
        if data_type in ['energy', 'psd']:
            if data_type == 'energy':
                required_columns.extend(['E1', 'E2', 'E3'])
            else:  # psd
                required_columns.extend(['P1', 'P2', 'P3'])

        missing_columns = [col for col in required_columns if col not in self.data_df.columns]
        if missing_columns:
            messagebox.showerror("数据错误", f"缺失必要数据列: {', '.join(missing_columns)}")
            return

        try:
            # 创建模式选择对话框
            mode_choice = simpledialog.askstring("可视化模式", "请选择模式(surface/scatter):", initialvalue='scatter')
            if not mode_choice:
                return

            # 准备数据
            depths = self.data_df['Depth'].values

            # 创建三维图表
            fig = go.Figure()

            # 添加三个剖面的数据
            profiles = []
            if data_type == 'speed':
                profiles = [
                    {'name': '剖面1-2', 'col': 'S1', 'y_val': 1},
                    {'name': '剖面1-3', 'col': 'S2', 'y_val': 2},
                    {'name': '剖面2-3', 'col': 'S3', 'y_val': 3}
                ]
                title = '桩基相对波速三维分布'
                xaxis_title = '相对波速 (%)'
            elif data_type == 'amplitude':
                profiles = [
                    {'name': '剖面1-2', 'col': 'A1', 'y_val': 1},
                    {'name': '剖面1-3', 'col': 'A2', 'y_val': 2},
                    {'name': '剖面2-3', 'col': 'A3', 'y_val': 3}
                ]
                title = '桩基波幅三维分布'
                xaxis_title = '波幅 (dB)'
            elif data_type == 'energy':
                profiles = [
                    {'name': '剖面1-2', 'col': 'E1', 'y_val': 1},
                    {'name': '剖面1-3', 'col': 'E2', 'y_val': 2},
                    {'name': '剖面2-3', 'col': 'E3', 'y_val': 3}
                ]
                title = '桩基能量三维分布'
                xaxis_title = '能量 (%)'
            else:  # psd
                profiles = [
                    {'name': '剖面1-2', 'col': 'P1', 'y_val': 1},
                    {'name': '剖面1-3', 'col': 'P2', 'y_val': 2},
                    {'name': '剖面2-3', 'col': 'P3', 'y_val': 3}
                ]
                title = '桩基PSD三维分布'
                xaxis_title = 'PSD'

            for profile in profiles:
                if profile['col'] not in self.data_df.columns:
                    continue

                if mode_choice == 'surface':
                    # 创建网格数据用于surface图
                    y_vals = np.array([profile['y_val'] - 0.2, profile['y_val'] + 0.2])
                    z_vals = depths

                    # 创建网格
                    y_grid, z_grid = np.meshgrid(y_vals, z_vals)

                    # 对于每个深度和y值，使用相同的x值
                    x_grid = np.tile(self.data_df[profile['col']].values[:, np.newaxis], (1, len(y_vals)))

                    # 创建自定义颜色刻度
                    custom_colorscale = [
                        [0, 'rgb(0,180,0)'],      # 绿色 - 正常
                        [0.33, 'rgb(255,255,0)'],  # 黄色 - 轻微畸变
                        [0.66, 'rgb(255,165,0)'],  # 橙色 - 明显畸变
                        [1, 'rgb(255,0,0)']       # 红色 - 严重畸变
                    ]

                    # 添加surface
                    fig.add_trace(go.Surface(
                        z=z_grid,
                        y=y_grid,
                        x=x_grid,
                        surfacecolor=np.tile(self.data_df[profile['col']].values[:, np.newaxis], (1, len(y_vals))),
                        colorscale=custom_colorscale,
                        opacity=0.8,
                        showscale=True,
                        colorbar=dict(
                            title=xaxis_title,
                            tickvals=self.get_category_thresholds(data_type),
                            ticktext=['严重畸变', '明显畸变', '轻微畸变', '正常']
                        ),
                        name=profile['name']
                    ))
                else:
                    # 为每个点根据分类标准分配颜色
                    colors = []
                    for value in self.data_df[profile['col']].values:
                        category = self.classify_value(value, data_type)
                        colors.append(self.get_category_color(category))

                    # 添加散点图
                    fig.add_trace(go.Scatter3d(
                        x=self.data_df[profile['col']].values,
                        y=np.ones(len(depths)) * profile['y_val'],
                        z=depths,
                        mode='markers',
                        marker=dict(
                            size=5,
                            color=colors,
                            opacity=0.8
                        ),
                        name=profile['name']
                    ))

            # 添加分类标准图例
            if mode_choice == 'scatter':
                # 为散点图添加图例
                for category, color in [('正常', 'rgb(0,180,0)'), ('轻微畸变', 'rgb(255,255,0)'),
                                       ('明显畸变', 'rgb(255,165,0)'), ('严重畸变', 'rgb(255,0,0)')]:
                    range_text = self.get_category_range_text(category, data_type)
                    fig.add_trace(go.Scatter3d(
                        x=[None],
                        y=[None],
                        z=[None],
                        mode='markers',
                        marker=dict(
                            size=10,
                            color=color,
                            opacity=1.0
                        ),
                        name=f"{category} ({range_text})"
                    ))

            # 设置图表布局
            fig.update_layout(
                title=title,
                scene=dict(
                    xaxis_title=xaxis_title,
                    yaxis_title='剖面',
                    yaxis=dict(
                        tickvals=[1, 2, 3],
                        ticktext=['剖面1-2', '剖面1-3', '剖面2-3']
                    ),
                    zaxis_title='深度 (m)',
                    zaxis=dict(autorange='reversed')
                ),
                width=1000,
                height=800,
                legend=dict(
                    title="分类标准",
                    yanchor="top",
                    y=0.99,
                    xanchor="left",
                    x=0.01
                )
            )

            # 显示图表
            fig.show()

        except Exception as e:
            print(f"可视化失败: {str(e)}")
            messagebox.showerror("可视化错误", f"三维可视化失败: {str(e)}")

    def classify_value(self, value, data_type):
        """根据值和类型进行分类"""
        enhanced_config = self.create_enhanced_config()

        if data_type == 'speed':
            thresholds = enhanced_config['speed_thresholds']
        elif data_type == 'amplitude':
            thresholds = enhanced_config['amp_thresholds']
        elif data_type == 'energy':
            thresholds = enhanced_config['energy_thresholds']
        elif data_type == 'psd':
            thresholds = enhanced_config['psd_thresholds']
        else:
            return '正常'

        for category, (min_val, max_val) in thresholds.items():
            if min_val <= value < max_val:
                return category

        return '正常'  # 默认分类

    def get_category_color(self, category):
        """获取分类对应的颜色"""
        category_colors = {
            '正常': 'rgb(0,180,0)',       # 绿色
            '轻微畸变': 'rgb(255,255,0)',  # 黄色
            '明显畸变': 'rgb(255,165,0)',  # 橙色
            '严重畸变': 'rgb(255,0,0)'     # 红色
        }
        return category_colors.get(category, 'rgb(128,128,128)')  # 默认灰色

    def get_category_thresholds(self, data_type):
        """获取分类阈值"""
        enhanced_config = self.create_enhanced_config()

        if data_type == 'speed':
            thresholds = enhanced_config['speed_thresholds']
        elif data_type == 'amplitude':
            thresholds = enhanced_config['amp_thresholds']
        elif data_type == 'energy':
            thresholds = enhanced_config['energy_thresholds']
        elif data_type == 'psd':
            thresholds = enhanced_config['psd_thresholds']
        else:
            return [0, 1, 2, 3]

        return [
            thresholds['严重畸变'][0],
            thresholds['明显畸变'][0],
            thresholds['轻微畸变'][0],
            thresholds['正常'][0]
        ]

    def get_category_range_text(self, category, data_type):
        """获取分类范围文本"""
        enhanced_config = self.create_enhanced_config()

        if data_type == 'speed':
            thresholds = enhanced_config['speed_thresholds']
            unit = '%'
        elif data_type == 'amplitude':
            thresholds = enhanced_config['amp_thresholds']
            unit = 'dB'
        elif data_type == 'energy':
            thresholds = enhanced_config['energy_thresholds']
            unit = ''
        elif data_type == 'psd':
            thresholds = enhanced_config['psd_thresholds']
            unit = ''
        else:
            return 'N/A'

        if category in thresholds:
            min_val, max_val = thresholds[category]
            return f"{min_val}-{max_val}{unit}"
        return 'N/A'

    def _analyze_and_preview_model(self, file_path, preview_text): 
        try:
            preview_text.delete(1.0, tk.END); preview_text.insert(tk.END, "[SEARCH] 正在分析模型文件...\n\n"); preview_text.update()
            with open(file_path, 'rb') as f: model_data = pickle.load(f)
            analysis_text = "[DATA] 模型分析结果:\n" + "=" * 40 + "\n\n"
            if isinstance(model_data, dict):
                if 'model' in model_data and ('preprocessor' in model_data or 'feature_extractor' in model_data and 'scaler' in model_data): 
                    analysis_text += "[START] 模型类型: 高级/优化模型 (包含预处理组件)\n"
                    if 'accuracy' in model_data: analysis_text += f"[CHART] 声明准确率: {model_data['accuracy']:.1%}\n" 
                    num_feat = model_data.get('feature_count', '未知') 
                    analysis_text += f"[CONFIG] 特征数量: {num_feat}\n"
                    if 'preprocessor' in model_data: analysis_text += "[ENERGY] 预处理器: 已包含\n"
                    if 'feature_extractor' in model_data: analysis_text += "🔩 特征提取器: 已包含\n"
                    if 'scaler' in model_data: analysis_text += "[COMPARE] 缩放器: 已包含\n"
                    analysis_text += "[DART] 推荐用途: 高精度分析\n\n"
                elif 'classifier_model' in model_data: 
                    analysis_text += "[CONFIG] 模型类型: 标准完整模型 (V1兼容)\n"
                    analysis_text += "[CHART] 预期准确率: (取决于训练)\n"
                    analysis_text += "[CONFIG] 特征数量: (通常54个标准特征)\n"
                    analysis_text += "[PROCESS] 组件: 分类器, 可能有缩放器/异常检测器\n"
                    analysis_text += "[DART] 推荐用途: 标准分析 (V1系统)\n\n"
                else:
                    analysis_text += "❓ 模型类型: 自定义字典格式\n"
                    analysis_text += f"[CLIPBOARD] 包含组件: {', '.join(model_data.keys())}\n\n"
            elif hasattr(model_data, 'predict'): 
                analysis_text += "[CONFIG] 模型类型: 单个分类器 (原始Scikit-learn模型)\n"
                analysis_text += "[CHART] 预期准确率: (取决于训练)\n"
                if hasattr(model_data, 'n_features_in_'): analysis_text += f"[CONFIG] 输入特征数 (模型期望): {model_data.n_features_in_}\n"
                else: analysis_text += "[CONFIG] 输入特征数: 未知 (模型未提供)\n"
                analysis_text += "[WARNING] 注意: 可能需要手动配置或加载额外的预处理步骤 (如缩放器)。\n"
                analysis_text += "[DART] 推荐用途: 基础分析, 或作为V2系统中的一部分。\n\n"
                if hasattr(model_data, '__class__'): analysis_text += f"🏷️ 模型类: {model_data.__class__.__name__}\n"
            else:
                analysis_text += "[ERROR] 模型类型: 未知格式\n[WARNING] 警告: 可能不兼容当前系统\n\n"
            
            analysis_text += "[SEARCH] 兼容性提示 (V2系统):\n" + "-" * 20 + "\n"
            analysis_text += "  - V2系统会尝试根据模型结构自动匹配或配置特征提取器。\n"
            analysis_text += "  - 高级模型 (含'feature_extractor'或'preprocessor') 通常能更好地发挥V2系统性能。\n"
            analysis_text += "  - 标准或单个分类器模型也可以加载，但可能需要注意特征兼容性。\n"
            analysis_text += "\n[TIP] 建议: 加载后，检查V2系统中的模型信息和特征提取器配置。"
            preview_text.delete(1.0, tk.END); preview_text.insert(tk.END, analysis_text)
        except Exception as e:
            error_text = f"[ERROR] 模型分析失败: {str(e)}\n\n可能的原因:\n- 文件格式不正确或已损坏\n- 不是有效的pickle模型文件\n\n请选择正确的.pkl模型文件。"
            preview_text.delete(1.0, tk.END); preview_text.insert(tk.END, error_text); traceback.print_exc()

def main():
    try: app = PileAnalyzerGZGUI(); app.run()
    except Exception as e: print(f"Application error: {str(e)}"); traceback.print_exc()

if __name__ == "__main__":
    main()
