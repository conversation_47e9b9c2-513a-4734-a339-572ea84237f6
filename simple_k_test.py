#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的K值计算测试，不依赖主程序
"""

def calculate_K_i_simple(I_ji_values):
    """
    计算K值的简单实现
    K_i = int((∑I(j,i)² / ∑I(j,i)) + 0.5)
    """
    if not I_ji_values:
        return 1
    
    sum_squares = sum(i**2 for i in I_ji_values)
    sum_values = sum(I_ji_values)
    
    if sum_values == 0:
        return 1
    
    K_i = int((sum_squares / sum_values) + 0.5)
    return K_i

def test_k_calculation():
    """测试K值计算"""
    
    print("=" * 60)
    print("K值计算公式测试")
    print("=" * 60)
    
    # 测试您提供的实际数据
    print("\n测试实际数据的K值计算：")
    
    # 深度 3.60m的数据
    print("\n深度 3.60m:")
    I_ji_values_360 = [4, 3, 3]  # 剖面1-2: 4, 剖面1-3: 3, 剖面2-3: 3
    K_i_360 = calculate_K_i_simple(I_ji_values_360)
    print(f"I(j,i)值: {I_ji_values_360}")
    print(f"计算过程: K_i = int((∑I(j,i)² / ∑I(j,i)) + 0.5)")
    print(f"         = int(({4**2} + {3**2} + {3**2}) / ({4} + {3} + {3}) + 0.5)")
    print(f"         = int((16 + 9 + 9) / 10 + 0.5)")
    print(f"         = int(34 / 10 + 0.5)")
    print(f"         = int(3.4 + 0.5)")
    print(f"         = int(3.9)")
    print(f"         = 3")
    print(f"实际计算结果: K_i = {K_i_360}")
    print(f"修复前错误结果: K_i = 4 (使用了最大值方法)")
    
    # 深度 3.70m的数据
    print("\n深度 3.70m:")
    I_ji_values_370 = [1, 2, 1]  # 剖面1-2: 1, 剖面1-3: 2, 剖面2-3: 1
    K_i_370 = calculate_K_i_simple(I_ji_values_370)
    print(f"I(j,i)值: {I_ji_values_370}")
    print(f"计算过程: K_i = int((∑I(j,i)² / ∑I(j,i)) + 0.5)")
    print(f"         = int(({1**2} + {2**2} + {1**2}) / ({1} + {2} + {1}) + 0.5)")
    print(f"         = int((1 + 4 + 1) / 4 + 0.5)")
    print(f"         = int(6 / 4 + 0.5)")
    print(f"         = int(1.5 + 0.5)")
    print(f"         = int(2.0)")
    print(f"         = 2")
    print(f"实际计算结果: K_i = {K_i_370}")
    print(f"修复前错误结果: K_i = 4 (受到深度范围内其他点影响)")
    
    # 深度 3.50m的数据
    print("\n深度 3.50m:")
    I_ji_values_350 = [1, 2, 2]  # 剖面1-2: 1, 剖面1-3: 2, 剖面2-3: 2
    K_i_350 = calculate_K_i_simple(I_ji_values_350)
    print(f"I(j,i)值: {I_ji_values_350}")
    print(f"计算过程: K_i = int((∑I(j,i)² / ∑I(j,i)) + 0.5)")
    print(f"         = int(({1**2} + {2**2} + {2**2}) / ({1} + {2} + {2}) + 0.5)")
    print(f"         = int((1 + 4 + 4) / 5 + 0.5)")
    print(f"         = int(9 / 5 + 0.5)")
    print(f"         = int(1.8 + 0.5)")
    print(f"         = int(2.3)")
    print(f"         = 2")
    print(f"实际计算结果: K_i = {K_i_350}")
    
    print("\n" + "=" * 60)
    print("修复总结:")
    print("=" * 60)
    print("修复前问题:")
    print("1. 使用了错误的计算方法：K值 = 深度范围内最大I(j,i)值")
    print("2. 深度范围影响导致一个点的高值影响整个范围")
    print("\n修复后:")
    print("1. 使用标准GZ方法公式：K_i = int((∑I(j,i)² / ∑I(j,i)) + 0.5)")
    print("2. 每个深度独立计算K值，不受其他深度影响")
    print("3. 符合GZ方法标准，计算结果更加准确")

if __name__ == "__main__":
    test_k_calculation()
